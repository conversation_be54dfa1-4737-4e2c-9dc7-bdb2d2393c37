use chrono::Utc;
use log::{debug, error, info, warn};
use rusqlite::{params, Connection, Result as SqliteResult, Transaction};
use std::error::Error;
use uuid::Uuid;

use crate::models::project_management::{
    DictionaryItem, DrugGroup, Personnel, Project, ProjectPagination, ProjectPersonnelRole,
    ProjectPersonnelWithDetails, ProjectQuery, ProjectSponsor, ProjectSponsorWithDetails,
    ProjectWithDetails, ResearchDrug, ResearchDrugWithDetails, Subsidy, SubsidyScheme, SubsidyWithDetails,
    RecruitmentCompanyPolicy, RecruitmentCompanyPolicyWithDetails, CreateRecruitmentPolicyRequest, UpdateRecruitmentPolicyRequest,
};

/// 项目管理仓库
pub struct ProjectManagementRepository {
    db_path: String,
}

impl ProjectManagementRepository {
    /// 创建新的项目管理仓库
    pub fn new(db_path: String) -> Self {
        Self { db_path }
    }

    /// 检查表是否存在
    fn table_exists(&self, conn: &Connection, table_name: &str) -> Result<bool, Box<dyn Error>> {
        let count: i64 = conn.query_row(
            "SELECT COUNT(*) FROM sqlite_master WHERE type='table' AND name=?",
            params![table_name],
            |row| row.get(0),
        )?;

        Ok(count > 0)
    }

    /// 检查列是否存在
    fn column_exists(&self, conn: &Connection, table_name: &str, column_name: &str) -> Result<bool, Box<dyn Error>> {
        let pragma_sql = format!("PRAGMA table_info({})", table_name);
        let mut stmt = conn.prepare(&pragma_sql)?;
        let rows = stmt.query_map([], |row| {
            let name: String = row.get(1)?;
            Ok(name)
        })?;

        for row in rows {
            if let Ok(name) = row {
                if name == column_name {
                    return Ok(true);
                }
            }
        }
        Ok(false)
    }

    /// 执行数据库迁移
    fn migrate_database(&self, conn: &Connection) -> Result<(), Box<dyn Error>> {
        info!("开始执行数据库迁移...");

        // 检查并添加research_drugs表的新字段
        let new_columns = vec![
            ("drug_type_item_id", "INTEGER"),              // 药物类型
            ("drug_classification_item_id", "INTEGER"),     // 药物分类
            ("usage_method_item_id", "INTEGER"),            // 用药方法
            ("usage_frequency_item_id", "INTEGER"),         // 用药频率
            ("dosage", "TEXT"),                             // 剂量
            ("share", "REAL"),                              // 份额/占比
            ("drug_characteristics", "TEXT"),               // 药物特性描述
            ("notes", "TEXT"),                              // 其他备注
        ];

        // 删除不再需要的字段（如果存在）
        let columns_to_drop = vec![
            "administration_route_item_id",  // 删除给药途径（与用药方法重复）
            "duration",                      // 删除持续时间（不必要的字段）
        ];

        for column_name in columns_to_drop {
            if self.column_exists(conn, "research_drugs", column_name)? {
                // SQLite 不支持 DROP COLUMN，但我们可以在查询中忽略这些字段
                info!("字段 {} 存在但将被忽略", column_name);
            }
        };

        for (column_name, column_type) in new_columns {
            if !self.column_exists(conn, "research_drugs", column_name)? {
                let alter_sql = format!("ALTER TABLE research_drugs ADD COLUMN {} {}", column_name, column_type);
                conn.execute(&alter_sql, [])?;
                info!("已添加字段: research_drugs.{}", column_name);
            } else {
                debug!("字段已存在: research_drugs.{}", column_name);
            }
        }

        // 初始化药物相关字典数据
        self.init_drug_dictionaries(conn)?;

        info!("数据库迁移完成");
        Ok(())
    }

    /// 初始化药物相关字典数据
    fn init_drug_dictionaries(&self, conn: &Connection) -> Result<(), Box<dyn Error>> {
        info!("开始初始化药物相关字典数据...");

        // 创建字典数据的通用函数
        let create_dictionary = |name: &str, description: &str, items: Vec<(&str, &str)>| -> Result<(), Box<dyn Error>> {
            // 检查字典是否已存在
            let exists: i64 = conn.query_row(
                "SELECT COUNT(*) FROM dictionaries WHERE name = ?",
                params![name],
                |row| row.get(0),
            )?;

            if exists == 0 {
                // 插入字典主表
                conn.execute(
                    "INSERT INTO dictionaries (name, description, type, created_at, updated_at, version)
                     VALUES (?, ?, 'list', datetime('now'), datetime('now'), 1)",
                    params![name, description],
                )?;

                // 获取字典ID
                let dict_id: i64 = conn.query_row(
                    "SELECT id FROM dictionaries WHERE name = ?",
                    params![name],
                    |row| row.get(0),
                )?;

                // 插入字典项
                for (key, value) in items {
                    conn.execute(
                        "INSERT INTO dictionary_items (dictionary_id, item_key, item_value, status)
                         VALUES (?, ?, ?, 'active')",
                        params![dict_id, key, value],
                    )?;
                }

                info!("已创建字典: {}", name);
            } else {
                debug!("字典已存在: {}", name);
            }
            Ok(())
        };

        // 创建药物分类字典
        create_dictionary(
            "药物分类",
            "药物分类（化学药物、生物制品等）",
            vec![
                ("chemical", "化学药物"),
                ("biological", "生物制品"),
                ("traditional_chinese", "中药"),
                ("vaccine", "疫苗"),
                ("medical_device", "医疗器械"),
                ("cell_therapy", "细胞治疗产品"),
                ("gene_therapy", "基因治疗产品"),
                ("other", "其他"),
            ]
        )?;

        // 创建用药方法字典
        create_dictionary(
            "用药方法",
            "药物使用方法（口服、注射等）",
            vec![
                ("oral", "口服"),
                ("injection", "注射"),
                ("intravenous", "静脉注射"),
                ("intramuscular", "肌肉注射"),
                ("subcutaneous", "皮下注射"),
                ("topical", "外用"),
                ("inhalation", "吸入"),
                ("sublingual", "舌下含服"),
                ("rectal", "直肠给药"),
                ("transdermal", "透皮给药"),
                ("other", "其他"),
            ]
        )?;

        // 创建用药频率字典
        create_dictionary(
            "用药频率",
            "药物使用频率（每日次数等）",
            vec![
                ("qd", "每日一次"),
                ("bid", "每日两次"),
                ("tid", "每日三次"),
                ("qid", "每日四次"),
                ("q6h", "每6小时一次"),
                ("q8h", "每8小时一次"),
                ("q12h", "每12小时一次"),
                ("weekly", "每周一次"),
                ("biweekly", "每两周一次"),
                ("monthly", "每月一次"),
                ("prn", "必要时"),
                ("single_dose", "单次给药"),
                ("other", "其他"),
            ]
        )?;

        // 给药途径字典已删除，与用药方法重复

        // 创建药物类型字典
        create_dictionary(
            "药物类型",
            "药物类型（研究药物、对照药物、安慰剂）",
            vec![
                ("study_drug", "研究药物"),
                ("control_drug", "对照药物"),
                ("placebo", "安慰剂"),
            ]
        )?;

        info!("药物相关字典数据初始化完成");
        Ok(())
    }

    /// 初始化所有相关表
    pub fn init_tables(&self) -> Result<(), Box<dyn Error>> {
        let conn = self.get_connection()?;

        // 检查表是否存在
        let tables = vec![
            "projects",
            "project_sponsors",
            "research_drugs",
            "drug_groups",
            "project_personnel_roles",
            "subsidy_schemes",
            "subsidies",
            "scheme_included_subsidies",
        ];

        info!("检查数据库表是否存在:");
        for table in &tables {
            let exists = self.table_exists(&conn, table)?;
            info!("表 {} {}", table, if exists { "存在" } else { "不存在" });
        }

        // 创建项目表
        conn.execute(
            "CREATE TABLE IF NOT EXISTS projects (
                project_id TEXT PRIMARY KEY,
                project_name TEXT NOT NULL,
                project_short_name TEXT NOT NULL,
                project_path TEXT,
                disease_item_id INTEGER,
                project_stage_item_id INTEGER,
                project_status_item_id INTEGER,
                recruitment_status_item_id INTEGER,
                contract_case_center INTEGER,
                contract_case_total INTEGER,
                project_start_date TEXT,
                last_updated TEXT
            )",
            [],
        )?;

        // 创建项目申办方表
        conn.execute(
            "CREATE TABLE IF NOT EXISTS project_sponsors (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                project_id TEXT NOT NULL,
                sponsor_item_id INTEGER NOT NULL,
                FOREIGN KEY (project_id) REFERENCES projects (project_id),
                UNIQUE (project_id, sponsor_item_id)
            )",
            [],
        )?;

        // 创建研究药物表
        conn.execute(
            "CREATE TABLE IF NOT EXISTS research_drugs (
                drug_info_id INTEGER PRIMARY KEY AUTOINCREMENT,
                project_id TEXT NOT NULL,
                research_drug TEXT NOT NULL,
                drug_classification_item_id INTEGER,
                usage_method_item_id INTEGER,
                usage_frequency_item_id INTEGER,
                dosage TEXT,
                administration_route_item_id INTEGER,
                duration TEXT,
                notes TEXT,
                FOREIGN KEY (project_id) REFERENCES projects (project_id)
            )",
            [],
        )?;

        // 创建药物分组表
        conn.execute(
            "CREATE TABLE IF NOT EXISTS drug_groups (
                group_id INTEGER PRIMARY KEY AUTOINCREMENT,
                project_id TEXT NOT NULL,
                drug_name TEXT NOT NULL,
                share REAL NOT NULL,
                FOREIGN KEY (project_id) REFERENCES projects (project_id)
            )",
            [],
        )?;

        // 创建项目人员角色表
        conn.execute(
            "CREATE TABLE IF NOT EXISTS project_personnel_roles (
                assignment_id INTEGER PRIMARY KEY AUTOINCREMENT,
                project_id TEXT NOT NULL,
                personnel_id INTEGER NOT NULL,
                role_item_id INTEGER NOT NULL,
                FOREIGN KEY (project_id) REFERENCES projects (project_id),
                UNIQUE (project_id, personnel_id, role_item_id)
            )",
            [],
        )?;

        // 创建补贴方案表
        conn.execute(
            "CREATE TABLE IF NOT EXISTS subsidy_schemes (
                scheme_id INTEGER PRIMARY KEY AUTOINCREMENT,
                project_id TEXT NOT NULL,
                scheme_name TEXT NOT NULL,
                total_amount REAL NOT NULL,
                FOREIGN KEY (project_id) REFERENCES projects (project_id)
            )",
            [],
        )?;

        // 创建补贴项表
        conn.execute(
            "CREATE TABLE IF NOT EXISTS subsidies (
                subsidy_item_id INTEGER PRIMARY KEY AUTOINCREMENT,
                project_id TEXT NOT NULL,
                subsidy_type_item_id INTEGER NOT NULL,
                unit_amount REAL NOT NULL,
                total_units INTEGER NOT NULL,
                unit_item_id INTEGER NOT NULL,
                total_amount REAL NOT NULL,
                last_updated TEXT,
                FOREIGN KEY (project_id) REFERENCES projects (project_id)
            )",
            [],
        )?;

        // 创建补贴方案包含的补贴项关联表
        conn.execute(
            "CREATE TABLE IF NOT EXISTS scheme_included_subsidies (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                scheme_id INTEGER NOT NULL,
                subsidy_item_id INTEGER NOT NULL,
                FOREIGN KEY (scheme_id) REFERENCES subsidy_schemes (scheme_id) ON DELETE CASCADE,
                FOREIGN KEY (subsidy_item_id) REFERENCES subsidies (subsidy_item_id) ON DELETE CASCADE,
                UNIQUE (scheme_id, subsidy_item_id)
            )",
            [],
        )?;

        // 确保外键约束已启用
        conn.execute("PRAGMA foreign_keys = ON", [])?;

        // 执行数据库迁移
        self.migrate_database(&conn)?;

        // 创建索引
        conn.execute("CREATE INDEX IF NOT EXISTS idx_project_sponsors_project_id ON project_sponsors(project_id)", [])?;
        conn.execute("CREATE INDEX IF NOT EXISTS idx_research_drugs_project_id ON research_drugs(project_id)", [])?;
        conn.execute(
            "CREATE INDEX IF NOT EXISTS idx_drug_groups_project_id ON drug_groups(project_id)",
            [],
        )?;
        conn.execute("CREATE INDEX IF NOT EXISTS idx_project_personnel_roles_project_id ON project_personnel_roles(project_id)", [])?;
        conn.execute("CREATE INDEX IF NOT EXISTS idx_subsidy_schemes_project_id ON subsidy_schemes(project_id)", [])?;
        conn.execute(
            "CREATE INDEX IF NOT EXISTS idx_subsidies_project_id ON subsidies(project_id)",
            [],
        )?;
        conn.execute("CREATE INDEX IF NOT EXISTS idx_scheme_included_subsidies_scheme_id ON scheme_included_subsidies(scheme_id)", [])?;
        conn.execute("CREATE INDEX IF NOT EXISTS idx_scheme_included_subsidies_subsidy_item_id ON scheme_included_subsidies(subsidy_item_id)", [])?;

        Ok(())
    }

    /// 获取数据库连接
    fn get_connection(&self) -> SqliteResult<Connection> {
        let conn = Connection::open(&self.db_path)?;
        // 确保外键约束已启用
        conn.execute("PRAGMA foreign_keys = ON", [])?;
        Ok(conn)
    }

    /// 获取项目列表（带分页和筛选）
    pub fn get_projects(&self, query: &ProjectQuery) -> Result<ProjectPagination, Box<dyn Error>> {
        let conn = self.get_connection()?;

        // 构建查询
        let mut sql = String::from(
            "SELECT p.project_id, p.project_name, p.project_short_name, p.project_path,
                    p.disease_item_id, p.project_stage_item_id, p.project_status_item_id,
                    p.recruitment_status_item_id, p.contract_case_center, p.contract_case_total,
                    p.project_start_date, p.last_updated
             FROM projects p",
        );

        let mut where_clauses = Vec::new();
        let mut params_values: Vec<Box<dyn rusqlite::ToSql>> = Vec::new();

        // 添加筛选条件
        if let Some(name) = &query.name {
            where_clauses.push("(p.project_name LIKE ? OR p.project_short_name LIKE ?)");
            let search_term = format!("%{}%", name);
            params_values.push(Box::new(search_term.clone()));
            params_values.push(Box::new(search_term));
        }

        if let Some(disease_id) = query.disease_item_id {
            where_clauses.push("p.disease_item_id = ?");
            params_values.push(Box::new(disease_id));
        }

        if let Some(stage_id) = query.project_stage_item_id {
            where_clauses.push("p.project_stage_item_id = ?");
            params_values.push(Box::new(stage_id));
        }

        if let Some(status_id) = query.project_status_item_id {
            where_clauses.push("p.project_status_item_id = ?");
            params_values.push(Box::new(status_id));
        }

        if let Some(recruitment_id) = query.recruitment_status_item_id {
            where_clauses.push("p.recruitment_status_item_id = ?");
            params_values.push(Box::new(recruitment_id));
        }

        // 添加WHERE子句
        if !where_clauses.is_empty() {
            sql.push_str(" WHERE ");
            sql.push_str(&where_clauses.join(" AND "));
        }

        // 添加排序
        let sort_by = query.sort_by.as_deref().unwrap_or("project_name");
        let sort_order = query.sort_order.as_deref().unwrap_or("asc");
        sql.push_str(&format!(" ORDER BY p.{} {}", sort_by, sort_order));

        // 计算总数
        let count_sql = format!("SELECT COUNT(*) FROM ({}) as count_query", sql);
        let total: i64 = conn.query_row(
            &count_sql,
            rusqlite::params_from_iter(params_values.iter().map(|p| p.as_ref())),
            |row| row.get(0),
        )?;

        // 添加分页
        let page = query.page.unwrap_or(1);
        let page_size = query.page_size.unwrap_or(10);
        let offset = (page - 1) * page_size;

        sql.push_str(&format!(" LIMIT {} OFFSET {}", page_size, offset));

        // 执行查询
        let mut stmt = conn.prepare(&sql)?;
        let params_slice: Vec<&dyn rusqlite::ToSql> =
            params_values.iter().map(|p| p.as_ref()).collect();

        let project_iter = stmt.query_map(params_slice.as_slice(), |row| {
            Ok(Project {
                project_id: row.get(0)?,
                project_name: row.get(1)?,
                project_short_name: row.get(2)?,
                project_path: row.get(3)?,
                disease_item_id: row.get(4)?,
                project_stage_item_id: row.get(5)?,
                project_status_item_id: row.get(6)?,
                recruitment_status_item_id: row.get(7)?,
                contract_case_center: row.get(8)?,
                contract_case_total: row.get(9)?,
                project_start_date: row.get(10)?,
                last_updated: row.get(11)?,
            })
        })?;

        // 收集项目并获取详细信息
        let mut projects = Vec::new();
        for project_result in project_iter {
            let project = project_result?;
            let project_with_details = self.get_project_with_basic_details(&conn, project)?;
            projects.push(project_with_details);
        }

        Ok(ProjectPagination {
            items: projects,
            total,
            page,
            page_size,
        })
    }

    /// 获取项目基本详情（仅包含字典项信息，不包含关联表）
    fn get_project_with_basic_details(
        &self,
        conn: &Connection,
        project: Project,
    ) -> Result<ProjectWithDetails, Box<dyn Error>> {
        // 获取疾病信息
        let disease = if let Some(disease_id) = project.disease_item_id {
            self.get_dictionary_item(conn, disease_id)?
        } else {
            None
        };

        // 获取项目阶段信息
        let project_stage = if let Some(stage_id) = project.project_stage_item_id {
            self.get_dictionary_item(conn, stage_id)?
        } else {
            None
        };

        // 获取项目状态信息
        let project_status = if let Some(status_id) = project.project_status_item_id {
            self.get_dictionary_item(conn, status_id)?
        } else {
            None
        };

        // 获取招募状态信息
        let recruitment_status = if let Some(recruitment_id) = project.recruitment_status_item_id {
            self.get_dictionary_item(conn, recruitment_id)?
        } else {
            None
        };

        Ok(ProjectWithDetails {
            project,
            disease,
            project_stage,
            project_status,
            recruitment_status,
            sponsors: None,
            research_drugs: None,
            drug_groups: None,
            personnel: None,
            subsidy_schemes: None,
            subsidies: None,
        })
    }

    /// 获取项目详情（包含所有关联数据）
    pub fn get_project_details(
        &self,
        project_id: &str,
    ) -> Result<Option<ProjectWithDetails>, Box<dyn Error>> {
        let conn = self.get_connection()?;

        // 获取项目基本信息
        let project = match self.get_project_by_id(&conn, project_id)? {
            Some(p) => p,
            None => return Ok(None),
        };

        // 获取基本详情（包含字典项）
        let mut project_details = self.get_project_with_basic_details(&conn, project)?;

        // 获取申办方
        project_details.sponsors = Some(self.get_project_sponsors(&conn, project_id)?);

        // 获取研究药物
        project_details.research_drugs = Some(self.get_research_drugs_with_details(&conn, project_id)?);

        // 获取药物分组
        project_details.drug_groups = Some(self.get_drug_groups(&conn, project_id)?);

        // 获取项目人员
        project_details.personnel = Some(self.get_project_personnel(&conn, project_id)?);

        // 获取补贴方案
        project_details.subsidy_schemes = Some(self.get_subsidy_schemes(&conn, project_id)?);

        // 获取补贴项
        project_details.subsidies = Some(self.get_subsidies(&conn, project_id)?);

        // 获取招募公司费用政策
        project_details.recruitment_policies = Some(self.get_recruitment_policies(project_id)?);

        Ok(Some(project_details))
    }

    /// 根据ID获取项目
    fn get_project_by_id(
        &self,
        conn: &Connection,
        project_id: &str,
    ) -> Result<Option<Project>, Box<dyn Error>> {
        match conn.query_row(
            "SELECT project_id, project_name, project_short_name, project_path,
                    disease_item_id, project_stage_item_id, project_status_item_id,
                    recruitment_status_item_id, contract_case_center, contract_case_total,
                    project_start_date, last_updated
             FROM projects
             WHERE project_id = ?",
            params![project_id],
            |row| {
                Ok(Project {
                    project_id: row.get(0)?,
                    project_name: row.get(1)?,
                    project_short_name: row.get(2)?,
                    project_path: row.get(3)?,
                    disease_item_id: row.get(4)?,
                    project_stage_item_id: row.get(5)?,
                    project_status_item_id: row.get(6)?,
                    recruitment_status_item_id: row.get(7)?,
                    contract_case_center: row.get(8)?,
                    contract_case_total: row.get(9)?,
                    project_start_date: row.get(10)?,
                    last_updated: row.get(11)?,
                })
            },
        ) {
            Ok(project) => Ok(Some(project)),
            Err(rusqlite::Error::QueryReturnedNoRows) => Ok(None),
            Err(e) => Err(e.into()),
        }
    }

    /// 获取项目申办方
    fn get_project_sponsors(
        &self,
        conn: &Connection,
        project_id: &str,
    ) -> Result<Vec<ProjectSponsorWithDetails>, Box<dyn Error>> {
        let mut stmt = conn.prepare(
            "SELECT id, project_id, sponsor_item_id
             FROM project_sponsors
             WHERE project_id = ?",
        )?;

        let sponsor_iter = stmt.query_map(params![project_id], |row| {
            Ok(ProjectSponsor {
                id: row.get(0)?,
                project_id: row.get(1)?,
                sponsor_item_id: row.get(2)?,
            })
        })?;

        let mut sponsors = Vec::new();
        for sponsor_result in sponsor_iter {
            let sponsor = sponsor_result?;
            let sponsor_item = self.get_dictionary_item(conn, sponsor.sponsor_item_id)?;

            sponsors.push(ProjectSponsorWithDetails {
                id: sponsor.id,
                project_id: sponsor.project_id,
                sponsor_item_id: sponsor.sponsor_item_id,
                sponsor: sponsor_item,
            });
        }

        Ok(sponsors)
    }

    /// 获取研究药物
    fn get_research_drugs(
        &self,
        conn: &Connection,
        project_id: &str,
    ) -> Result<Vec<ResearchDrug>, Box<dyn Error>> {
        let mut stmt = conn.prepare(
            "SELECT drug_info_id, project_id, research_drug, 
                    drug_type_item_id, drug_classification_item_id, usage_method_item_id, 
                    usage_frequency_item_id, dosage, share, drug_characteristics, notes
             FROM research_drugs
             WHERE project_id = ?",
        )?;

        let drug_iter = stmt.query_map(params![project_id], |row| {
            Ok(ResearchDrug {
                drug_info_id: row.get(0)?,
                project_id: row.get(1)?,
                research_drug: row.get(2)?,
                drug_type_item_id: row.get(3)?,
                drug_classification_item_id: row.get(4)?,
                usage_method_item_id: row.get(5)?,
                usage_frequency_item_id: row.get(6)?,
                dosage: row.get(7)?,
                share: row.get(8)?,
                drug_characteristics: row.get(9)?,
                notes: row.get(10)?,
            })
        })?;

        let mut drugs = Vec::new();
        for drug_result in drug_iter {
            drugs.push(drug_result?);
        }

        Ok(drugs)
    }

    /// 获取带详情的研究药物
    fn get_research_drugs_with_details(
        &self,
        conn: &Connection,
        project_id: &str,
    ) -> Result<Vec<ResearchDrugWithDetails>, Box<dyn Error>> {
        let mut stmt = conn.prepare(
            "SELECT rd.drug_info_id, rd.project_id, rd.research_drug, 
                    rd.drug_type_item_id, rd.drug_classification_item_id, 
                    rd.usage_method_item_id, rd.usage_frequency_item_id, 
                    rd.dosage, rd.share, rd.drug_characteristics, rd.notes,
                    dt.item_id as dt_item_id, dt.dictionary_id as dt_dict_id, 
                    dt.item_key as dt_key, dt.item_value as dt_value, 
                    dt.item_description as dt_desc, dt.status as dt_status,
                    dc.item_id as dc_item_id, dc.dictionary_id as dc_dict_id, 
                    dc.item_key as dc_key, dc.item_value as dc_value, 
                    dc.item_description as dc_desc, dc.status as dc_status,
                    um.item_id as um_item_id, um.dictionary_id as um_dict_id, 
                    um.item_key as um_key, um.item_value as um_value, 
                    um.item_description as um_desc, um.status as um_status,
                    uf.item_id as uf_item_id, uf.dictionary_id as uf_dict_id, 
                    uf.item_key as uf_key, uf.item_value as uf_value, 
                    uf.item_description as uf_desc, uf.status as uf_status
             FROM research_drugs rd
             LEFT JOIN dictionary_items dt ON rd.drug_type_item_id = dt.item_id
             LEFT JOIN dictionary_items dc ON rd.drug_classification_item_id = dc.item_id
             LEFT JOIN dictionary_items um ON rd.usage_method_item_id = um.item_id
             LEFT JOIN dictionary_items uf ON rd.usage_frequency_item_id = uf.item_id
             WHERE rd.project_id = ?",
        )?;

        let drug_iter = stmt.query_map(params![project_id], |row| {
            // 构建药物类型字典项
            let drug_type = if row.get::<_, Option<i64>>(11)?.is_some() {
                Some(DictionaryItem {
                    item_id: row.get(11)?,
                    dictionary_id: row.get(12)?,
                    item_key: row.get(13)?,
                    item_value: row.get(14)?,
                    item_description: row.get(15)?,
                    status: row.get(16)?,
                })
            } else {
                None
            };

            // 构建药物分类字典项
            let drug_classification = if row.get::<_, Option<i64>>(17)?.is_some() {
                Some(DictionaryItem {
                    item_id: row.get(17)?,
                    dictionary_id: row.get(18)?,
                    item_key: row.get(19)?,
                    item_value: row.get(20)?,
                    item_description: row.get(21)?,
                    status: row.get(22)?,
                })
            } else {
                None
            };

            // 构建用药方法字典项
            let usage_method = if row.get::<_, Option<i64>>(23)?.is_some() {
                Some(DictionaryItem {
                    item_id: row.get(23)?,
                    dictionary_id: row.get(24)?,
                    item_key: row.get(25)?,
                    item_value: row.get(26)?,
                    item_description: row.get(27)?,
                    status: row.get(28)?,
                })
            } else {
                None
            };

            // 构建用药频率字典项
            let usage_frequency = if row.get::<_, Option<i64>>(29)?.is_some() {
                Some(DictionaryItem {
                    item_id: row.get(29)?,
                    dictionary_id: row.get(30)?,
                    item_key: row.get(31)?,
                    item_value: row.get(32)?,
                    item_description: row.get(33)?,
                    status: row.get(34)?,
                })
            } else {
                None
            };

            Ok(ResearchDrugWithDetails {
                drug_info_id: row.get(0)?,
                project_id: row.get(1)?,
                research_drug: row.get(2)?,
                drug_type_item_id: row.get(3)?,
                drug_classification_item_id: row.get(4)?,
                usage_method_item_id: row.get(5)?,
                usage_frequency_item_id: row.get(6)?,
                dosage: row.get(7)?,
                share: row.get(8)?,
                drug_characteristics: row.get(9)?,
                notes: row.get(10)?,
                drug_type,
                drug_classification,
                usage_method,
                usage_frequency,
            })
        })?;

        let mut drugs = Vec::new();
        for drug_result in drug_iter {
            drugs.push(drug_result?);
        }

        Ok(drugs)
    }

    /// 获取药物分组
    fn get_drug_groups(
        &self,
        conn: &Connection,
        project_id: &str,
    ) -> Result<Vec<DrugGroup>, Box<dyn Error>> {
        let mut stmt = conn.prepare(
            "SELECT group_id, project_id, drug_name, share
             FROM drug_groups
             WHERE project_id = ?",
        )?;

        let group_iter = stmt.query_map(params![project_id], |row| {
            Ok(DrugGroup {
                group_id: row.get(0)?,
                project_id: row.get(1)?,
                drug_name: row.get(2)?,
                share: row.get(3)?,
            })
        })?;

        let mut groups = Vec::new();
        for group_result in group_iter {
            groups.push(group_result?);
        }

        Ok(groups)
    }

    /// 获取项目人员
    fn get_project_personnel(
        &self,
        conn: &Connection,
        project_id: &str,
    ) -> Result<Vec<ProjectPersonnelWithDetails>, Box<dyn Error>> {
        let mut stmt = conn.prepare(
            "SELECT ppr.assignment_id, ppr.project_id, ppr.personnel_id, ppr.role_item_id,
                    s.name, s.gender, s.birthday, s.phone, s.email, s.position_item_id, s.isPI, s.organization
             FROM project_personnel_roles ppr
             JOIN staff s ON ppr.personnel_id = s.id
             WHERE ppr.project_id = ?"
        )?;

        let personnel_iter = stmt.query_map(params![project_id], |row| {
            Ok(ProjectPersonnelWithDetails {
                assignment_id: row.get(0)?,
                project_id: row.get(1)?,
                personnel_id: row.get(2)?,
                role_item_id: row.get(3)?,
                personnel: Some(Personnel {
                    id: row.get(2)?,
                    name: row.get(4)?,
                    gender: row.get(5)?,
                    birthday: row.get(6)?,
                    phone: row.get(7)?,
                    email: row.get(8)?,
                    position_item_id: row.get(9)?,
                    isPI: row.get(10)?,
                    organization: row.get(11)?,
                }),
                role: None,
            })
        })?;

        let mut personnel_list = Vec::new();
        for personnel_result in personnel_iter {
            let mut personnel = personnel_result?;
            personnel.role = self.get_dictionary_item(conn, personnel.role_item_id)?;
            personnel_list.push(personnel);
        }

        Ok(personnel_list)
    }

    /// 获取补贴方案
    fn get_subsidy_schemes(
        &self,
        conn: &Connection,
        project_id: &str,
    ) -> Result<Vec<SubsidyScheme>, Box<dyn Error>> {
        info!("获取项目 {} 的补贴方案", project_id);

        let mut stmt = conn.prepare(
            "SELECT scheme_id, project_id, scheme_name, total_amount
             FROM subsidy_schemes
             WHERE project_id = ?",
        )?;

        let scheme_iter = stmt.query_map(params![project_id], |row| {
            Ok(SubsidyScheme {
                scheme_id: row.get(0)?,
                project_id: row.get(1)?,
                scheme_name: row.get(2)?,
                total_amount: row.get(3)?,
                included_subsidies: None, // 先设为None，后面再填充
            })
        })?;

        let mut schemes = Vec::new();
        for scheme_result in scheme_iter {
            let mut scheme = scheme_result?;
            info!(
                "找到补贴方案: ID={}, 名称={}, 金额={}",
                scheme.scheme_id.unwrap_or(0),
                scheme.scheme_name,
                scheme.total_amount
            );

            // 获取该方案包含的补贴项ID列表
            let scheme_id = scheme.scheme_id.unwrap_or(0);
            if scheme_id > 0 {
                let mut subsidy_stmt = conn.prepare(
                    "SELECT subsidy_item_id
                     FROM scheme_included_subsidies
                     WHERE scheme_id = ?",
                )?;

                let subsidy_ids_iter =
                    subsidy_stmt.query_map(params![scheme_id], |row| row.get::<_, i64>(0))?;

                let mut included_subsidies = Vec::new();
                for id_result in subsidy_ids_iter {
                    let subsidy_id = id_result?;
                    info!("方案 {} 包含补贴项 ID={}", scheme.scheme_name, subsidy_id);
                    included_subsidies.push(subsidy_id);
                }

                if !included_subsidies.is_empty() {
                    info!(
                        "方案 {} 共包含 {} 个补贴项",
                        scheme.scheme_name,
                        included_subsidies.len()
                    );
                    scheme.included_subsidies = Some(included_subsidies);
                } else {
                    info!("方案 {} 没有包含补贴项", scheme.scheme_name);
                }
            } else {
                info!("方案ID无效，无法获取包含的补贴项");
            }

            schemes.push(scheme);
        }

        info!("共找到 {} 个补贴方案", schemes.len());

        Ok(schemes)
    }

    /// 获取补贴项
    fn get_subsidies(
        &self,
        conn: &Connection,
        project_id: &str,
    ) -> Result<Vec<SubsidyWithDetails>, Box<dyn Error>> {
        let mut stmt = conn.prepare(
            "SELECT subsidy_item_id, project_id, subsidy_type_item_id, unit_amount,
                    total_units, unit_item_id, total_amount, last_updated
             FROM subsidies
             WHERE project_id = ?",
        )?;

        let subsidy_iter = stmt.query_map(params![project_id], |row| {
            Ok(Subsidy {
                subsidy_item_id: row.get(0)?,
                project_id: row.get(1)?,
                subsidy_type_item_id: row.get(2)?,
                unit_amount: row.get(3)?,
                total_units: row.get(4)?,
                unit_item_id: row.get(5)?,
                total_amount: row.get(6)?,
                last_updated: row.get(7)?,
            })
        })?;

        let mut subsidies = Vec::new();
        for subsidy_result in subsidy_iter {
            let subsidy = subsidy_result?;
            let subsidy_type = self.get_dictionary_item(conn, subsidy.subsidy_type_item_id)?;
            let unit = self.get_dictionary_item(conn, subsidy.unit_item_id)?;

            subsidies.push(SubsidyWithDetails {
                subsidy_item_id: subsidy.subsidy_item_id,
                project_id: subsidy.project_id,
                subsidy_type_item_id: subsidy.subsidy_type_item_id,
                unit_amount: subsidy.unit_amount,
                total_units: subsidy.total_units,
                unit_item_id: subsidy.unit_item_id,
                total_amount: subsidy.total_amount,
                last_updated: subsidy.last_updated,
                subsidy_type,
                unit,
            });
        }

        Ok(subsidies)
    }

    /// 获取字典项
    fn get_dictionary_item(
        &self,
        conn: &Connection,
        item_id: i64,
    ) -> Result<Option<DictionaryItem>, Box<dyn Error>> {
        match conn.query_row(
            "SELECT item_id, dictionary_id, item_key, item_value, item_description, status
             FROM dictionary_items
             WHERE item_id = ?",
            params![item_id],
            |row| {
                Ok(DictionaryItem {
                    item_id: row.get(0)?,
                    dictionary_id: row.get(1)?,
                    item_key: row.get(2)?,
                    item_value: row.get(3)?,
                    item_description: row.get(4)?,
                    status: row.get(5)?,
                })
            },
        ) {
            Ok(item) => Ok(Some(item)),
            Err(rusqlite::Error::QueryReturnedNoRows) => Ok(None),
            Err(e) => Err(e.into()),
        }
    }

    /// 创建新项目（使用事务）
    pub fn create_project(&self, request: &Project) -> Result<String, Box<dyn Error>> {
        let mut conn = self.get_connection()?;
        let tx = conn.transaction()?;

        // 生成项目ID
        let project_id = request
            .project_id
            .clone()
            .unwrap_or_else(|| Uuid::new_v4().to_string());
        let now = Utc::now().to_rfc3339();

        // 插入项目基本信息
        tx.execute(
            "INSERT INTO projects (
                project_id, project_name, project_short_name, project_path,
                disease_item_id, project_stage_item_id, project_status_item_id,
                recruitment_status_item_id, contract_case_center, contract_case_total,
                project_start_date, last_updated
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
            params![
                project_id,
                request.project_name,
                request.project_short_name,
                request.project_path,
                request.disease_item_id,
                request.project_stage_item_id,
                request.project_status_item_id,
                request.recruitment_status_item_id,
                request.contract_case_center,
                request.contract_case_total,
                request.project_start_date,
                now
            ],
        )?;

        // 提交事务
        tx.commit()?;

        Ok(project_id)
    }

    /// 更新项目（使用事务）
    pub fn update_project(&self, request: &Project) -> Result<(), Box<dyn Error>> {
        if request.project_id.is_none() {
            return Err("项目ID不能为空".into());
        }

        let project_id = request.project_id.as_ref().unwrap();
        let mut conn = self.get_connection()?;
        let tx = conn.transaction()?;

        let now = Utc::now().to_rfc3339();

        // 更新项目基本信息
        tx.execute(
            "UPDATE projects SET
                project_name = ?,
                project_short_name = ?,
                project_path = ?,
                disease_item_id = ?,
                project_stage_item_id = ?,
                project_status_item_id = ?,
                recruitment_status_item_id = ?,
                contract_case_center = ?,
                contract_case_total = ?,
                project_start_date = ?,
                last_updated = ?
             WHERE project_id = ?",
            params![
                request.project_name,
                request.project_short_name,
                request.project_path,
                request.disease_item_id,
                request.project_stage_item_id,
                request.project_status_item_id,
                request.recruitment_status_item_id,
                request.contract_case_center,
                request.contract_case_total,
                request.project_start_date,
                now,
                project_id
            ],
        )?;

        // 提交事务
        tx.commit()?;

        Ok(())
    }

    /// 保存项目完整信息（包含所有关联数据，使用事务）
    pub fn save_project_with_details(
        &self,
        request: &Project,
        sponsors: Option<&Vec<ProjectSponsor>>,
        research_drugs: Option<&Vec<ResearchDrug>>,
        drug_groups: Option<&Vec<DrugGroup>>,
        personnel: Option<&Vec<ProjectPersonnelRole>>,
        subsidy_schemes: &Vec<SubsidyScheme>,
        subsidies: Option<&Vec<Subsidy>>,
    ) -> Result<String, Box<dyn Error>> {
        let mut conn = self.get_connection()?;

        // 检查项目是否存在
        let is_new = if let Some(id) = &request.project_id {
            match self.get_project_by_id(&conn, id)? {
                None => true,
                Some(_) => false,
            }
        } else {
            true
        };

        let tx = conn.transaction()?;

        // 生成项目ID或使用现有ID
        let project_id = if is_new {
            let id = request
                .project_id
                .clone()
                .unwrap_or_else(|| Uuid::new_v4().to_string());

            // 插入新项目
            let now = Utc::now().to_rfc3339();
            tx.execute(
                "INSERT INTO projects (
                    project_id, project_name, project_short_name, project_path,
                    disease_item_id, project_stage_item_id, project_status_item_id,
                    recruitment_status_item_id, contract_case_center, contract_case_total,
                    project_start_date, last_updated
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
                params![
                    id,
                    request.project_name,
                    request.project_short_name,
                    request.project_path,
                    request.disease_item_id,
                    request.project_stage_item_id,
                    request.project_status_item_id,
                    request.recruitment_status_item_id,
                    request.contract_case_center,
                    request.contract_case_total,
                    request.project_start_date,
                    now
                ],
            )?;

            id
        } else {
            // 更新现有项目
            let id = request.project_id.as_ref().unwrap().clone();
            let now = Utc::now().to_rfc3339();

            tx.execute(
                "UPDATE projects SET
                    project_name = ?,
                    project_short_name = ?,
                    project_path = ?,
                    disease_item_id = ?,
                    project_stage_item_id = ?,
                    project_status_item_id = ?,
                    recruitment_status_item_id = ?,
                    contract_case_center = ?,
                    contract_case_total = ?,
                    project_start_date = ?,
                    last_updated = ?
                 WHERE project_id = ?",
                params![
                    request.project_name,
                    request.project_short_name,
                    request.project_path,
                    request.disease_item_id,
                    request.project_stage_item_id,
                    request.project_status_item_id,
                    request.recruitment_status_item_id,
                    request.contract_case_center,
                    request.contract_case_total,
                    request.project_start_date,
                    now,
                    id
                ],
            )?;

            id
        };

        // 处理申办方
        if let Some(sponsors_list) = sponsors {
            // 删除现有申办方
            tx.execute(
                "DELETE FROM project_sponsors WHERE project_id = ?",
                params![project_id],
            )?;

            // 插入新申办方
            for sponsor in sponsors_list {
                tx.execute(
                    "INSERT INTO project_sponsors (project_id, sponsor_item_id)
                     VALUES (?, ?)",
                    params![project_id, sponsor.sponsor_item_id],
                )?;
            }
        }

        // 处理研究药物
        if let Some(drugs_list) = research_drugs {
            // 删除现有研究药物
            tx.execute(
                "DELETE FROM research_drugs WHERE project_id = ?",
                params![project_id],
            )?;

            // 插入新研究药物
            for drug in drugs_list {
                tx.execute(
                    "INSERT INTO research_drugs (
                        project_id, research_drug, drug_type_item_id, 
                        drug_classification_item_id, usage_method_item_id, 
                        usage_frequency_item_id, dosage, share, 
                        drug_characteristics, notes
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
                    params![
                        project_id, 
                        drug.research_drug,
                        drug.drug_type_item_id,
                        drug.drug_classification_item_id,
                        drug.usage_method_item_id,
                        drug.usage_frequency_item_id,
                        drug.dosage,
                        drug.share,
                        drug.drug_characteristics,
                        drug.notes
                    ],
                )?;
            }
        }

        // 注意: 药物分组功能已合并到研究药物表中的share字段
        // 为了向后兼容，保留drug_groups的处理，但建议使用research_drugs中的share字段
        if let Some(groups_list) = drug_groups {
            // 删除现有药物分组
            tx.execute(
                "DELETE FROM drug_groups WHERE project_id = ?",
                params![project_id],
            )?;

            // 为了向后兼容，仍然插入drug_groups，但在前端应优先使用research_drugs中的share字段
            for group in groups_list {
                tx.execute(
                    "INSERT INTO drug_groups (project_id, drug_name, share)
                     VALUES (?, ?, ?)",
                    params![project_id, group.drug_name, group.share],
                )?;
            }
        }

        // 处理项目人员
        if let Some(personnel_list) = personnel {
            // 删除现有项目人员
            tx.execute(
                "DELETE FROM project_personnel_roles WHERE project_id = ?",
                params![project_id],
            )?;

            // 插入新项目人员
            for person in personnel_list {
                tx.execute(
                    "INSERT INTO project_personnel_roles (project_id, personnel_id, role_item_id)
                     VALUES (?, ?, ?)",
                    params![project_id, person.personnel_id, person.role_item_id],
                )?;
            }
        }

        // 先处理补贴项，再处理补贴方案，确保补贴项ID已经存在
        let mut subsidy_id_map = std::collections::HashMap::new();

        if let Some(subsidies_list) = subsidies {
            debug!(
                "开始处理补贴项 (save_project_with_details)，数量: {}",
                subsidies_list.len()
            );

            // 删除现有补贴项前，先删除关联的方案包含关系
            debug!("准备删除旧的 scheme_included_subsidies (基于旧补贴项)");
            let deleted_relations = tx.execute(
                "DELETE FROM scheme_included_subsidies
                 WHERE subsidy_item_id IN (
                     SELECT subsidy_item_id FROM subsidies WHERE project_id = ?
                 )",
                params![project_id],
            )?;
            debug!(
                "已删除旧的 scheme_included_subsidies (基于旧补贴项): {} 条",
                deleted_relations
            );

            // 删除现有补贴项
            debug!("准备删除旧的 subsidies");
            let deleted_subsidies = tx.execute(
                "DELETE FROM subsidies WHERE project_id = ?",
                params![project_id],
            )?;
            debug!("已删除旧的 subsidies: {} 条", deleted_subsidies);

            // 插入新补贴项
            let now = Utc::now().to_rfc3339();
            for (index, subsidy) in subsidies_list.iter().enumerate() {
                debug!("准备插入补贴项 #{}", index);

                // 记录原始ID（如果有）
                let original_id = subsidy.subsidy_item_id;
                debug!(
                    "补贴项 #{} 的前端 ID (original_id): {:?}",
                    index, original_id
                );

                tx.execute(
                    "INSERT INTO subsidies (
                        project_id, subsidy_type_item_id, unit_amount,
                        total_units, unit_item_id, total_amount, last_updated
                    ) VALUES (?, ?, ?, ?, ?, ?, ?)",
                    params![
                        project_id,
                        subsidy.subsidy_type_item_id,
                        subsidy.unit_amount,
                        subsidy.total_units,
                        subsidy.unit_item_id,
                        subsidy.total_amount,
                        now
                    ],
                )?;

                // 获取新插入的补贴项ID
                let new_subsidy_id = tx.last_insert_rowid();
                debug!(
                    "补贴项 #{} 新插入的 DB ID (new_subsidy_id): {}",
                    index, new_subsidy_id
                );

                // 始终记录ID映射关系，无论原始ID是否存在或是什么值（包括负数临时ID）
                // 确保前端传入的 subsidy_item_id (可能是正数、负数或None) 被用作 Key
                if let Some(frontend_id) = original_id {
                    subsidy_id_map.insert(frontend_id, new_subsidy_id);
                    debug!(
                        "记录ID映射: 前端ID {} -> 新DB ID {}",
                        frontend_id, new_subsidy_id
                    );
                } else {
                    // 如果前端没有提供ID (理论上不应发生，因为我们加了临时ID)，也记录一下
                    // 这里用 new_subsidy_id 作为 key 可能不是最佳选择，但作为备用记录
                    // 或者可以考虑生成一个临时的 UUID key？但现在前端保证有ID了
                    warn!("前端未提供补贴项ID (original_id is None)，无法为补贴项 #{} 创建映射。新DB ID: {}", index, new_subsidy_id);
                    // subsidy_id_map.insert(new_subsidy_id, new_subsidy_id); // 暂时注释掉，期望前端总是有ID
                }
            }
            debug!("补贴项处理完成，ID映射表: {:?}", subsidy_id_map);
        } else {
            debug!("没有传入补贴项数据");
        }

        // 处理补贴方案
        debug!(
            "开始处理补贴方案 (save_project_with_details)，接收数量: {}",
            subsidy_schemes.len()
        );

        // 删除现有补贴方案 (ON DELETE CASCADE 会处理 scheme_included_subsidies)
        debug!("准备删除旧的 subsidy_schemes (关联删除 scheme_included_subsidies)");
        let deleted_schemes = tx.execute(
            "DELETE FROM subsidy_schemes WHERE project_id = ?",
            params![project_id],
        )?;
        debug!(
            "已删除旧的 subsidy_schemes: {} 条 (关联记录应已级联删除)",
            deleted_schemes
        );

        // 插入新补贴方案
        if !subsidy_schemes.is_empty() {
            debug!("开始循环插入新的补贴方案");
            for (scheme_index, scheme) in subsidy_schemes.iter().enumerate() {
                debug!("准备插入方案 #{}", scheme_index);
                debug!(
                    "方案 #{} 数据: scheme_name={}, total_amount={}, included_subsidies={:?}",
                    scheme_index,
                    scheme.scheme_name,
                    scheme.total_amount,
                    scheme.included_subsidies
                );

                // 插入方案基本信息
                tx.execute(
                    "INSERT INTO subsidy_schemes (project_id, scheme_name, total_amount)
                        VALUES (?, ?, ?)",
                    params![project_id, scheme.scheme_name, scheme.total_amount],
                )?;

                // 获取新插入的方案ID
                let new_scheme_id = tx.last_insert_rowid();
                debug!(
                    "方案 #{} 新插入的 DB ID (new_scheme_id): {}",
                    scheme_index, new_scheme_id
                );

                // 处理方案包含的补贴项
                if let Some(included_ids) = &scheme.included_subsidies {
                    debug!(
                        "开始处理方案 #{} 包含的补贴项，数量: {}",
                        scheme_index,
                        included_ids.len()
                    );
                    for (subsidy_rel_index, original_subsidy_id) in included_ids.iter().enumerate()
                    {
                        debug!(
                            "处理方案 #{} 的关联 #{}: 前端补贴项 ID (original_subsidy_id) = {}",
                            scheme_index, subsidy_rel_index, original_subsidy_id
                        );

                        // 使用之前记录的ID映射找到新的补贴项ID
                        // original_subsidy_id 现在可能是正数（旧项）或负数（新项的临时ID）
                        match subsidy_id_map.get(original_subsidy_id) {
                            Some(new_subsidy_id) => {
                                debug!(
                                    "ID映射查找成功: 前端ID {} -> 新DB ID {}",
                                    original_subsidy_id, new_subsidy_id
                                );
                                debug!(
                                    "准备插入关联: scheme_id={}, subsidy_item_id={}",
                                    new_scheme_id, new_subsidy_id
                                );
                                match tx.execute(
                                    "INSERT INTO scheme_included_subsidies (scheme_id, subsidy_item_id)
                                        VALUES (?, ?)",
                                    params![new_scheme_id, new_subsidy_id],
                                ) {
                                    Ok(rows_affected) => debug!("插入 scheme_included_subsidies 成功: scheme_id={}, subsidy_item_id={}, 影响行数={}",
                                                                new_scheme_id, new_subsidy_id, rows_affected),
                                    Err(e) => error!("插入 scheme_included_subsidies 失败: scheme_id={}, subsidy_item_id={}, 错误: {}",
                                                        new_scheme_id, new_subsidy_id, e),
                                }
                            }
                            None => {
                                warn!("ID映射查找失败: 未找到前端补贴项 ID {} 对应的数据库 ID。无法为方案 #{} 创建关联。",
                                        original_subsidy_id, scheme_index);
                            }
                        }
                    }
                    debug!("方案 #{} 包含的补贴项处理完成", scheme_index);
                } else {
                    debug!(
                        "方案 #{} 没有包含的补贴项 (included_subsidies is None or empty)",
                        scheme_index
                    );
                }
            }
        } else {
            debug!("传入的补贴方案 Vec 为空，无需插入新方案。");
        }
        debug!("补贴方案处理完成");

        // 提交事务
        debug!("准备提交事务 (save_project_with_details)");
        tx.commit()?;
        debug!("事务提交成功 (save_project_with_details)");

        Ok(project_id)
    }

    /// 删除项目（包含所有关联数据，使用事务）
    pub fn delete_project(&self, project_id: &str) -> Result<(), Box<dyn Error>> {
        let mut conn = self.get_connection()?;
        let tx = conn.transaction()?;

        // 删除所有关联数据
        tx.execute(
            "DELETE FROM project_sponsors WHERE project_id = ?",
            params![project_id],
        )?;
        tx.execute(
            "DELETE FROM research_drugs WHERE project_id = ?",
            params![project_id],
        )?;
        tx.execute(
            "DELETE FROM drug_groups WHERE project_id = ?",
            params![project_id],
        )?;
        tx.execute(
            "DELETE FROM project_personnel_roles WHERE project_id = ?",
            params![project_id],
        )?;

        // 删除补贴方案关联
        tx.execute(
            "DELETE FROM scheme_included_subsidies
             WHERE scheme_id IN (SELECT scheme_id FROM subsidy_schemes WHERE project_id = ?)",
            params![project_id],
        )?;

        tx.execute(
            "DELETE FROM subsidy_schemes WHERE project_id = ?",
            params![project_id],
        )?;
        tx.execute(
            "DELETE FROM subsidies WHERE project_id = ?",
            params![project_id],
        )?;

        // 删除项目本身
        tx.execute(
            "DELETE FROM projects WHERE project_id = ?",
            params![project_id],
        )?;

        // 提交事务
        tx.commit()?;

        Ok(())
    }

    /// 检查数据库表和数据
    pub fn check_database_tables(&self) -> Result<String, Box<dyn Error>> {
        let conn = self.get_connection()?;
        let mut result = String::new();

        // 检查表是否存在
        let tables = vec![
            "projects",
            "project_sponsors",
            "research_drugs",
            "drug_groups",
            "project_personnel_roles",
            "subsidy_schemes",
            "subsidies",
            "scheme_included_subsidies",
        ];

        result.push_str("数据库表状态:\n");
        for table in &tables {
            let exists = self.table_exists(&conn, table)?;
            result.push_str(&format!(
                "表 {} {}\n",
                table,
                if exists { "存在" } else { "不存在" }
            ));
        }

        // 检查各表中的数据量
        result.push_str("\n各表数据量:\n");
        for table in &tables {
            if self.table_exists(&conn, table)? {
                let count: i64 =
                    conn.query_row(&format!("SELECT COUNT(*) FROM {}", table), [], |row| {
                        row.get(0)
                    })?;
                result.push_str(&format!("表 {} 中有 {} 条记录\n", table, count));
            }
        }

        // 检查scheme_included_subsidies表的结构
        if self.table_exists(&conn, "scheme_included_subsidies")? {
            let mut stmt = conn.prepare("PRAGMA table_info(scheme_included_subsidies)")?;
            let columns = stmt.query_map([], |row| {
                Ok(format!(
                    "{}: {}",
                    row.get::<_, String>(1)?,
                    row.get::<_, String>(2)?
                ))
            })?;

            result.push_str("\nscheme_included_subsidies表结构:\n");
            for column in columns {
                result.push_str(&format!("{}\n", column?));
            }
        }

        // 检查外键约束
        let mut stmt = conn.prepare("PRAGMA foreign_key_list(scheme_included_subsidies)")?;
        let foreign_keys = stmt.query_map([], |row| {
            Ok(format!(
                "外键: {} -> {}.{}",
                row.get::<_, String>(3)?,
                row.get::<_, String>(2)?,
                row.get::<_, String>(4)?
            ))
        })?;

        result.push_str("\nscheme_included_subsidies表外键约束:\n");
        for fk in foreign_keys {
            result.push_str(&format!("{}\n", fk?));
        }

        Ok(result)
    }

    /// 重置数据库表
    pub fn reset_database_tables(&self) -> Result<(), Box<dyn Error>> {
        let mut conn = self.get_connection()?;

        // 获取所有表名
        let tables = vec![
            "scheme_included_subsidies",
            "subsidy_schemes",
            "subsidies",
            "project_personnel_roles",
            "drug_groups",
            "research_drugs",
            "project_sponsors",
            "projects",
        ];

        // 开始事务
        let tx = conn.transaction()?;

        // 删除所有表
        for table in &tables {
            info!("删除表: {}", table);
            tx.execute(&format!("DROP TABLE IF EXISTS {}", table), [])?;
        }

        // 提交事务
        tx.commit()?;

        // 重新初始化表
        self.init_tables()?;

        Ok(())
    }

    // ==================== 招募公司费用政策管理方法 ====================

    /// 获取项目的招募公司费用政策列表
    pub fn get_recruitment_policies(&self, project_id: &str) -> Result<Vec<RecruitmentCompanyPolicyWithDetails>, Box<dyn Error>> {
        let conn = self.get_connection()?;

        let sql = "
            SELECT
                rcp.policy_id,
                rcp.project_id,
                rcp.recruitment_company_item_id,
                rcp.informed_consent_fee,
                rcp.randomization_fee,
                rcp.fee_currency,
                rcp.payment_method,
                rcp.description,
                rcp.is_active,
                rcp.notes,
                rcp.created_at,
                rcp.updated_at,
                di.item_id,
                di.dictionary_id,
                di.item_key,
                di.item_value,
                di.item_description,
                di.status
            FROM recruitment_company_policies rcp
            LEFT JOIN dictionary_items di ON rcp.recruitment_company_item_id = di.item_id
            WHERE rcp.project_id = ?
            ORDER BY rcp.created_at DESC
        ";

        let mut stmt = conn.prepare(sql)?;
        let policy_iter = stmt.query_map(params![project_id], |row| {
            let recruitment_company = if row.get::<_, Option<i64>>(12)?.is_some() {
                Some(DictionaryItem {
                    item_id: row.get(12)?,
                    dictionary_id: row.get(13)?,
                    item_key: row.get(14)?,
                    item_value: row.get(15)?,
                    item_description: row.get(16)?,
                    status: row.get(17)?,
                })
            } else {
                None
            };

            Ok(RecruitmentCompanyPolicyWithDetails {
                policy_id: row.get(0)?,
                project_id: row.get(1)?,
                recruitment_company_item_id: row.get(2)?,
                informed_consent_fee: row.get(3)?,
                randomization_fee: row.get(4)?,
                fee_currency: row.get(5)?,
                payment_method: row.get(6)?,
                description: row.get(7)?,
                is_active: row.get(8)?,
                notes: row.get(9)?,
                created_at: row.get(10)?,
                updated_at: row.get(11)?,
                recruitment_company,
            })
        })?;

        let mut policies = Vec::new();
        for policy_result in policy_iter {
            policies.push(policy_result?);
        }

        Ok(policies)
    }

    /// 获取单个招募公司费用政策详情
    pub fn get_recruitment_policy(&self, policy_id: i64) -> Result<Option<RecruitmentCompanyPolicyWithDetails>, Box<dyn Error>> {
        let conn = self.get_connection()?;

        let sql = "
            SELECT
                rcp.policy_id,
                rcp.project_id,
                rcp.recruitment_company_item_id,
                rcp.informed_consent_fee,
                rcp.randomization_fee,
                rcp.fee_currency,
                rcp.payment_method,
                rcp.description,
                rcp.is_active,
                rcp.notes,
                rcp.created_at,
                rcp.updated_at,
                di.item_id,
                di.dictionary_id,
                di.item_key,
                di.item_value,
                di.item_description,
                di.status
            FROM recruitment_company_policies rcp
            LEFT JOIN dictionary_items di ON rcp.recruitment_company_item_id = di.item_id
            WHERE rcp.policy_id = ?
        ";

        let result = conn.query_row(sql, params![policy_id], |row| {
            let recruitment_company = if row.get::<_, Option<i64>>(12)?.is_some() {
                Some(DictionaryItem {
                    item_id: row.get(12)?,
                    dictionary_id: row.get(13)?,
                    item_key: row.get(14)?,
                    item_value: row.get(15)?,
                    item_description: row.get(16)?,
                    status: row.get(17)?,
                })
            } else {
                None
            };

            Ok(RecruitmentCompanyPolicyWithDetails {
                policy_id: row.get(0)?,
                project_id: row.get(1)?,
                recruitment_company_item_id: row.get(2)?,
                informed_consent_fee: row.get(3)?,
                randomization_fee: row.get(4)?,
                fee_currency: row.get(5)?,
                payment_method: row.get(6)?,
                description: row.get(7)?,
                is_active: row.get(8)?,
                notes: row.get(9)?,
                created_at: row.get(10)?,
                updated_at: row.get(11)?,
                recruitment_company,
            })
        });

        match result {
            Ok(policy) => Ok(Some(policy)),
            Err(rusqlite::Error::QueryReturnedNoRows) => Ok(None),
            Err(e) => Err(Box::new(e)),
        }
    }

    /// 创建招募公司费用政策
    pub fn create_recruitment_policy(&self, request: &CreateRecruitmentPolicyRequest) -> Result<i64, Box<dyn Error>> {
        let conn = self.get_connection()?;

        let sql = "
            INSERT INTO recruitment_company_policies (
                project_id,
                recruitment_company_item_id,
                informed_consent_fee,
                randomization_fee,
                fee_currency,
                payment_method,
                description,
                is_active,
                notes,
                created_at,
                updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, 1, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
        ";

        conn.execute(
            sql,
            params![
                request.project_id,
                request.recruitment_company_item_id,
                request.informed_consent_fee,
                request.randomization_fee,
                request.fee_currency,
                request.payment_method,
                request.description,
                request.notes
            ],
        )?;

        let policy_id = conn.last_insert_rowid();
        Ok(policy_id)
    }

    /// 更新招募公司费用政策
    pub fn update_recruitment_policy(&self, request: &UpdateRecruitmentPolicyRequest) -> Result<(), Box<dyn Error>> {
        let conn = self.get_connection()?;

        let sql = "
            UPDATE recruitment_company_policies
            SET
                recruitment_company_item_id = ?,
                informed_consent_fee = ?,
                randomization_fee = ?,
                fee_currency = ?,
                payment_method = ?,
                description = ?,
                is_active = ?,
                notes = ?,
                updated_at = CURRENT_TIMESTAMP
            WHERE policy_id = ?
        ";

        let rows_affected = conn.execute(
            sql,
            params![
                request.recruitment_company_item_id,
                request.informed_consent_fee,
                request.randomization_fee,
                request.fee_currency,
                request.payment_method,
                request.description,
                request.is_active,
                request.notes,
                request.policy_id
            ],
        )?;

        if rows_affected == 0 {
            return Err("招募公司费用政策不存在".into());
        }

        Ok(())
    }

    /// 删除招募公司费用政策
    pub fn delete_recruitment_policy(&self, policy_id: i64) -> Result<(), Box<dyn Error>> {
        let conn = self.get_connection()?;

        let sql = "DELETE FROM recruitment_company_policies WHERE policy_id = ?";
        let rows_affected = conn.execute(sql, params![policy_id])?;

        if rows_affected == 0 {
            return Err("招募公司费用政策不存在".into());
        }

        Ok(())
    }
}
