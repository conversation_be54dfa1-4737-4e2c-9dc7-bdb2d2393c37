<script lang="ts">
  import { onMount } from 'svelte';
  import { Button } from '$lib/components/ui/button';
  import Input from '$lib/components/ui/input.svelte';
  import Label from '$lib/components/ui/label.svelte';
  import { Card, CardContent, CardHeader, CardTitle } from '$lib/components/ui/card';
  import { Badge } from '$lib/components/ui/badge';
  import { Plus, Edit, Trash2, DollarSign, Users, AlertCircle } from 'lucide-svelte';
  import { projectManagementService, type RecruitmentCompanyPolicyWithDetails, type CreateRecruitmentPolicyRequest, type UpdateRecruitmentPolicyRequest } from '$lib/services/projectManagementService';
  import { sqliteDictionaryService } from '$lib/services/sqliteDictionaryService';

  // 组件属性
  const props = $props<{
    projectId: string;
  }>();

  // 状态变量
  let policies = $state<RecruitmentCompanyPolicyWithDetails[]>([]);
  let recruitmentCompanies = $state<any[]>([]);
  let isLoading = $state(false);
  let error = $state<string | null>(null);
  let showAddDialog = $state(false);
  let showEditDialog = $state(false);
  let editingPolicy = $state<RecruitmentCompanyPolicyWithDetails | null>(null);

  // 表单数据
  let formData = $state({
    recruitment_company_item_id: 0,
    informed_consent_fee: 0,
    randomization_fee: 0,
    fee_currency: 'CNY',
    payment_method: '',
    description: '',
    notes: ''
  });

  // 加载数据
  onMount(async () => {
    await loadData();
  });

  async function loadData() {
    isLoading = true;
    error = null;

    try {
      // 并行加载招募公司费用政策和招募公司字典
      const [policiesData, companiesData] = await Promise.all([
        projectManagementService.getRecruitmentPolicies(props.projectId),
        sqliteDictionaryService.getDictionaryItems('招募公司')
      ]);

      policies = policiesData;
      recruitmentCompanies = companiesData;
    } catch (err) {
      error = err instanceof Error ? err.message : '加载数据失败';
      console.error('加载招募公司费用政策数据失败:', err);
    } finally {
      isLoading = false;
    }
  }

  // 重置表单
  function resetForm() {
    formData = {
      recruitment_company_item_id: 0,
      informed_consent_fee: 0,
      randomization_fee: 0,
      fee_currency: 'CNY',
      payment_method: '',
      description: '',
      notes: ''
    };
  }

  // 打开添加对话框
  function openAddDialog() {
    resetForm();
    showAddDialog = true;
  }

  // 打开编辑对话框
  function openEditDialog(policy: RecruitmentCompanyPolicyWithDetails) {
    editingPolicy = policy;
    formData = {
      recruitment_company_item_id: policy.recruitment_company_item_id,
      informed_consent_fee: policy.informed_consent_fee,
      randomization_fee: policy.randomization_fee,
      fee_currency: policy.fee_currency || 'CNY',
      payment_method: policy.payment_method || '',
      description: policy.description || '',
      notes: policy.notes || ''
    };
    showEditDialog = true;
  }

  // 关闭对话框
  function closeDialogs() {
    showAddDialog = false;
    showEditDialog = false;
    editingPolicy = null;
    resetForm();
  }

  // 保存政策
  async function savePolicy() {
    if (formData.recruitment_company_item_id === 0) {
      alert('请选择招募公司');
      return;
    }

    if (formData.informed_consent_fee < 0 || formData.randomization_fee < 0) {
      alert('费用不能为负数');
      return;
    }

    try {
      if (showEditDialog && editingPolicy) {
        // 更新
        const updateRequest: UpdateRecruitmentPolicyRequest = {
          policy_id: editingPolicy.policy_id!,
          recruitment_company_item_id: formData.recruitment_company_item_id,
          informed_consent_fee: formData.informed_consent_fee,
          randomization_fee: formData.randomization_fee,
          fee_currency: formData.fee_currency,
          payment_method: formData.payment_method,
          description: formData.description,
          is_active: true,
          notes: formData.notes
        };
        await projectManagementService.updateRecruitmentPolicy(updateRequest);
      } else {
        // 创建
        const createRequest: CreateRecruitmentPolicyRequest = {
          project_id: props.projectId,
          recruitment_company_item_id: formData.recruitment_company_item_id,
          informed_consent_fee: formData.informed_consent_fee,
          randomization_fee: formData.randomization_fee,
          fee_currency: formData.fee_currency,
          payment_method: formData.payment_method,
          description: formData.description,
          notes: formData.notes
        };
        await projectManagementService.createRecruitmentPolicy(createRequest);
      }

      closeDialogs();
      await loadData();
    } catch (err) {
      error = err instanceof Error ? err.message : '保存失败';
      console.error('保存招募公司费用政策失败:', err);
    }
  }

  // 删除政策
  async function deletePolicy(policy: RecruitmentCompanyPolicyWithDetails) {
    if (!confirm(`确定要删除 ${policy.recruitment_company?.item_value} 的费用政策吗？`)) {
      return;
    }

    try {
      await projectManagementService.deleteRecruitmentPolicy(policy.policy_id!);
      await loadData();
    } catch (err) {
      error = err instanceof Error ? err.message : '删除失败';
      console.error('删除招募公司费用政策失败:', err);
    }
  }

  // 格式化货币
  function formatCurrency(amount: number, currency: string = 'CNY'): string {
    return new Intl.NumberFormat('zh-CN', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2
    }).format(amount);
  }

  // 计算费用差额
  function calculateFeeDifference(randomizationFee: number, informedConsentFee: number): number {
    return randomizationFee - informedConsentFee;
  }
</script>

<div class="space-y-6">
  <!-- 标题和操作按钮 -->
  <div class="flex justify-between items-center">
    <div>
      <h2 class="text-2xl font-bold">招募公司费用政策</h2>
      <p class="text-gray-600 mt-1">管理不同招募公司的知情费用和随机费用政策</p>
    </div>
    <Button onclick={openAddDialog} class="flex items-center gap-2">
      <Plus class="h-4 w-4" />
      添加费用政策
    </Button>
  </div>

  <!-- 费用规则说明 -->
  <Card class="border-blue-200 bg-blue-50">
    <CardContent class="pt-4">
      <div class="flex items-start gap-3">
        <AlertCircle class="h-5 w-5 text-blue-600 mt-0.5" />
        <div class="space-y-2 text-sm">
          <p class="font-medium text-blue-900">费用规则说明：</p>
          <ul class="space-y-1 text-blue-800">
            <li>• <strong>知情费用</strong>：患者签署知情同意书后的推荐费用</li>
            <li>• <strong>随机费用</strong>：患者随机成功并使用临床研究药物后的费用（包含知情费用）</li>
            <li>• 如果患者随机失败，则只支付知情费用</li>
          </ul>
        </div>
      </div>
    </CardContent>
  </Card>

  <!-- 错误提示 -->
  {#if error}
    <div class="bg-red-50 border border-red-200 rounded-lg p-4">
      <div class="flex items-center gap-2 text-red-800">
        <AlertCircle class="h-4 w-4" />
        <span class="font-medium">错误：{error}</span>
      </div>
    </div>
  {/if}

  <!-- 加载状态 -->
  {#if isLoading}
    <div class="flex justify-center py-8">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
    </div>
  {:else if policies.length === 0}
    <!-- 空状态 -->
    <Card class="text-center py-12">
      <CardContent>
        <Users class="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 class="text-lg font-medium text-gray-900 mb-2">暂无费用政策</h3>
        <p class="text-gray-600 mb-4">还没有配置任何招募公司的费用政策</p>
        <Button onclick={openAddDialog} class="flex items-center gap-2 mx-auto">
          <Plus class="h-4 w-4" />
          添加第一个费用政策
        </Button>
      </CardContent>
    </Card>
  {:else}
    <!-- 费用政策对比表格 -->
    <Card>
      <CardHeader>
        <CardTitle class="flex items-center gap-2">
          <DollarSign class="h-5 w-5" />
          费用政策对比
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div class="overflow-x-auto">
          <table class="w-full border-collapse">
            <thead>
              <tr class="border-b">
                <th class="text-left py-3 px-4 font-medium">招募公司</th>
                <th class="text-right py-3 px-4 font-medium">知情费用</th>
                <th class="text-right py-3 px-4 font-medium">随机费用</th>
                <th class="text-right py-3 px-4 font-medium">费用差额</th>
                <th class="text-center py-3 px-4 font-medium">货币</th>
                <th class="text-center py-3 px-4 font-medium">状态</th>
                <th class="text-center py-3 px-4 font-medium">操作</th>
              </tr>
            </thead>
            <tbody>
              {#each policies as policy}
                <tr class="border-b hover:bg-gray-50">
                  <td class="py-3 px-4">
                    <div>
                      <div class="font-medium">{policy.recruitment_company?.item_value || '未知公司'}</div>
                      {#if policy.description}
                        <div class="text-sm text-gray-600">{policy.description}</div>
                      {/if}
                    </div>
                  </td>
                  <td class="py-3 px-4 text-right font-mono">
                    {formatCurrency(policy.informed_consent_fee, policy.fee_currency)}
                  </td>
                  <td class="py-3 px-4 text-right font-mono">
                    {formatCurrency(policy.randomization_fee, policy.fee_currency)}
                  </td>
                  <td class="py-3 px-4 text-right font-mono">
                    <span class="text-green-600">
                      +{formatCurrency(calculateFeeDifference(policy.randomization_fee, policy.informed_consent_fee), policy.fee_currency)}
                    </span>
                  </td>
                  <td class="py-3 px-4 text-center">
                    <Badge variant="outline">{policy.fee_currency || 'CNY'}</Badge>
                  </td>
                  <td class="py-3 px-4 text-center">
                    <Badge variant={policy.is_active ? 'default' : 'secondary'}>
                      {policy.is_active ? '启用' : '禁用'}
                    </Badge>
                  </td>
                  <td class="py-3 px-4 text-center">
                    <div class="flex justify-center gap-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onclick={() => openEditDialog(policy)}
                        class="h-8 w-8 p-0"
                      >
                        <Edit class="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onclick={() => deletePolicy(policy)}
                        class="h-8 w-8 p-0 text-red-600 hover:text-red-700"
                      >
                        <Trash2 class="h-4 w-4" />
                      </Button>
                    </div>
                  </td>
                </tr>
              {/each}
            </tbody>
          </table>
        </div>
      </CardContent>
    </Card>
  {/if}
</div>

<!-- 添加/编辑费用政策对话框 -->
{#if showAddDialog || showEditDialog}
  <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4 max-h-[90vh] overflow-y-auto">
      <div class="p-6">
        <h3 class="text-lg font-semibold mb-4">
          {showEditDialog ? '编辑费用政策' : '添加费用政策'}
        </h3>

        <div class="space-y-4">
          <!-- 招募公司选择 -->
          <div>
            <Label for="company">招募公司 *</Label>
            <select
              id="company"
              bind:value={formData.recruitment_company_item_id}
              class="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              required
            >
              <option value={0}>请选择招募公司</option>
              {#each recruitmentCompanies as company}
                <option value={company.item_id}>{company.item_value}</option>
              {/each}
            </select>
          </div>

          <!-- 知情费用 -->
          <div>
            <Label for="informed_fee">知情费用 *</Label>
            <Input
              id="informed_fee"
              type="number"
              step="0.01"
              min="0"
              bind:value={formData.informed_consent_fee}
              placeholder="0.00"
              required
            />
          </div>

          <!-- 随机费用 -->
          <div>
            <Label for="randomization_fee">随机费用 *</Label>
            <Input
              id="randomization_fee"
              type="number"
              step="0.01"
              min="0"
              bind:value={formData.randomization_fee}
              placeholder="0.00"
              required
            />
            <p class="text-xs text-gray-600 mt-1">随机费用应包含知情费用</p>
          </div>

          <!-- 货币类型 -->
          <div>
            <Label for="currency">货币类型</Label>
            <select
              id="currency"
              bind:value={formData.fee_currency}
              class="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="CNY">人民币 (CNY)</option>
              <option value="USD">美元 (USD)</option>
              <option value="EUR">欧元 (EUR)</option>
            </select>
          </div>

          <!-- 支付方式 -->
          <div>
            <Label for="payment_method">支付方式</Label>
            <Input
              id="payment_method"
              bind:value={formData.payment_method}
              placeholder="如：银行转账、现金等"
            />
          </div>

          <!-- 描述 -->
          <div>
            <Label for="description">描述</Label>
            <textarea
              id="description"
              bind:value={formData.description}
              rows="3"
              class="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="费用政策的详细说明"
            ></textarea>
          </div>

          <!-- 备注 -->
          <div>
            <Label for="notes">备注</Label>
            <textarea
              id="notes"
              bind:value={formData.notes}
              rows="2"
              class="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="其他备注信息"
            ></textarea>
          </div>
        </div>

        <!-- 对话框按钮 -->
        <div class="flex justify-end gap-3 mt-6">
          <Button variant="outline" onclick={closeDialogs}>
            取消
          </Button>
          <Button onclick={savePolicy}>
            {showEditDialog ? '更新' : '创建'}
          </Button>
        </div>
      </div>
    </div>
  </div>
{/if}
