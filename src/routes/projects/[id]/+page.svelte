<script lang="ts">
  import { onMount } from 'svelte';

  import { projectManagementService, type ProjectWithDetails, type DrugGroup, type ProjectExportData } from '$lib/services/projectManagementService';
  import { fileSystemService } from '$lib/services/fileSystemService';
  import { Button } from '$lib/components/ui/button';
  import Input from '$lib/components/ui/input.svelte';
  import { goto } from '$app/navigation';
  import * as Tabs from '$lib/components/ui/tabs.svelte';

  import ProjectBasicInfo from '$lib/components/project/ProjectBasicInfo.svelte';
  import ProjectSponsors from '$lib/components/project/ProjectSponsors.svelte';
  import ProjectDrugs from '$lib/components/project/ProjectDrugs.svelte';
  import ProjectPersonnel from '$lib/components/project/ProjectPersonnel.svelte';
  import ProjectSubsidies from '$lib/components/project/ProjectSubsidies.svelte';
  import ProjectCriteriaSummary from '$lib/components/project/ProjectCriteriaSummary.svelte';
  import ProjectFileExplorer from '$lib/components/project/ProjectFileExplorer.svelte';
  import ProjectRecruitmentPolicies from '$lib/components/project/ProjectRecruitmentPolicies.svelte';
  import { ruleDesignerService, type ProjectCriterionWithRule } from '$lib/services/ruleDesignerService';
  import { ArrowLeft, Save, FileDown, Users, Building, Pill, Clipboard, DollarSign, Pencil, Trash, ClipboardList, FileText } from 'lucide-svelte';
  import * as Tooltip from "$lib/components/ui/tooltip";

  // 获取项目ID
  export let data;
  let projectId = data?.id;

  // 状态管理
  let projectDetails: ProjectWithDetails | null = null;
  let isLoading = false;
  let isSaving = false;
  let isDeleting = false;
  let error: string | null = null;
  let successMessage: string | null = null;
  let activeTab = 'basic-info';
  let showDeleteConfirm = false;
  let confirmProjectName = '';
  let deleteConfirmError: string | null = null;

  // 加载项目详情
  async function loadProjectDetails() {
    if (projectId === 'new') {
      // 创建新项目
      projectDetails = {
        project: {
          project_name: '',
          project_short_name: '',
        },
        sponsors: [],
        research_drugs: [],
        drug_groups: [],
        personnel: [],
        subsidy_schemes: [],
        subsidies: []
      };
      return;
    }

    isLoading = true;
    error = null;

    try {
      const result = await projectManagementService.getProjectDetails(projectId);
      projectDetails = result;
    } catch (err) {
      error = err instanceof Error ? err.message : String(err);
    } finally {
      isLoading = false;
    }
  }

  // 导出项目详情为Markdown
  async function exportProjectDetailsToMarkdown(options = {
    basicInfo: true,
    sponsors: true,
    drugs: true,
    personnel: true,
    subsidies: true,
    criteria: true
  }) {
    if (!projectDetails) {
      alert('没有可导出的数据');
      return;
    }

    isLoading = true;
    error = null;

    try {
      // 准备导出内容
      const content = [
        `# 项目详情: ${projectDetails.project.project_short_name || ''}`,
        '',
      ];

      // 添加基本信息
      if (options.basicInfo) {
        content.push(
          '## 基本信息',
          `- 项目名称: ${projectDetails.project.project_name || '-'}`,
          `- 项目简称: ${projectDetails.project.project_short_name || '-'}`,
          `- 疾病: ${projectDetails.disease?.item_value || '-'}`,
          `- 研究分期: ${projectDetails.project_stage?.item_value || '-'}`,
          `- 项目状态: ${projectDetails.project_status?.item_value || '-'}`,
          `- 招募状态: ${projectDetails.recruitment_status?.item_value || '-'}`,
          `- 项目启动日期: ${projectDetails.project.project_start_date || '-'}`,
          ''
        );
      }

      // 添加申办方与合同信息
      if (options.sponsors) {
        content.push('## 申办方与合同信息', '', '### 申办方');

        // 添加申办方信息
        if (projectDetails.sponsors && projectDetails.sponsors.length > 0) {
          projectDetails.sponsors.forEach(sponsor => {
            content.push(`- ${sponsor.sponsor?.item_value || '未知申办方'}`);
          });
        } else {
          content.push('- 暂无申办方数据');
        }

        // 添加合同信息
        content.push('', '### 合同信息');
        content.push(`- 合同中心数: ${projectDetails.project.contract_case_center || '0'}`);
        content.push(`- 合同例数: ${projectDetails.project.contract_case_total || '0'}`);
        content.push('');
      }

      // 添加研究药物信息
      if (options.drugs) {
        content.push('## 研究药物信息');

        if (projectDetails.research_drugs && projectDetails.research_drugs.length > 0) {
          projectDetails.research_drugs.forEach(drug => {
            content.push(`### ${drug.research_drug}`);
            
            // 添加药物详细信息
            const details = [];
            
            // 药物类型（新增）
            if (drug.drug_type?.item_value) {
              details.push(`- **药物类型**: ${drug.drug_type.item_value}`);
            }
            
            if (drug.drug_classification?.item_value) {
              details.push(`- **药物分类**: ${drug.drug_classification.item_value}`);
            }
            if (drug.usage_method?.item_value) {
              details.push(`- **用药方法**: ${drug.usage_method.item_value}`);
            }
            if (drug.usage_frequency?.item_value) {
              details.push(`- **用药频率**: ${drug.usage_frequency.item_value}`);
            }
            if (drug.dosage) {
              details.push(`- **剂量信息**: ${drug.dosage}`);
            }
            if (drug.administration_route?.item_value) {
              details.push(`- **给药途径**: ${drug.administration_route.item_value}`);
            }
            if (drug.duration) {
              details.push(`- **用药持续时间**: ${drug.duration}`);
            }
            
            // 份额信息（新增）
            if (drug.share !== undefined && drug.share !== null) {
              details.push(`- **份额/占比**: ${drug.share}`);
            }
            
            // 药物特性（新增）
            if (drug.drug_characteristics) {
              details.push(`- **药物特性**: ${drug.drug_characteristics}`);
            }
            
            if (drug.notes) {
              details.push(`- **其他备注**: ${drug.notes}`);
            }
            
            if (details.length > 0) {
              content.push(...details);
            } else {
              content.push('- 暂无详细信息');
            }
            content.push(''); // 空行分隔
          });
          
          // 添加药物分组统计信息
          const drugsWithShare = projectDetails.research_drugs.filter(drug => drug.share !== undefined && drug.share !== null);
          if (drugsWithShare.length > 1) {
            content.push('### 药物占比统计');
            const totalShare = drugsWithShare.reduce((sum, drug) => sum + (drug.share || 0), 0);
            drugsWithShare.forEach(drug => {
              const percentage = totalShare > 0 ? Math.round(((drug.share || 0) / totalShare) * 100) : 0;
              content.push(`- ${drug.research_drug}: ${drug.share} (${percentage}%)`);
            });
            content.push('');
          }
        } else {
          content.push('- 暂无研究药物数据');
          content.push('');
        }
      }

      // 添加研究人员信息
      if (options.personnel) {
        content.push('## 研究人员');
        if (projectDetails.personnel && projectDetails.personnel.length > 0) {
          // 按角色分组人员
          const roleGroups = groupPersonnelByRole(projectDetails.personnel);

          roleGroups.forEach(roleGroup => {
            content.push(`### ${roleGroup.roleName}`);
            roleGroup.personnel.forEach(({ person }) => {
              content.push(`- ${person.personnel?.name || '未知人员'}`);
            });
          });
        } else {
          content.push('- 暂无研究人员数据');
        }
        content.push('');
      }

      // 添加补贴信息
      if (options.subsidies) {
        content.push('## 补贴信息');
        if (projectDetails.subsidy_schemes && projectDetails.subsidy_schemes.length > 0) {
          content.push('### 补贴方案');
          projectDetails.subsidy_schemes.forEach(scheme => {
            content.push(`- ${scheme.scheme_name || '未命名方案'}: ¥${scheme.total_amount || 0}`);
            // 可选：导出方案包含的补贴项
            if (projectDetails?.subsidies) {
              const includedItems = projectDetails.subsidies.filter(sub => scheme.included_subsidies?.includes(sub.subsidy_item_id || -1)) || [];
              if (includedItems.length > 0) {
                includedItems.forEach(item => {
                  content.push(`  - ${item.subsidy_type?.item_value || '未知类型'}: ${item.unit_amount} × ${item.total_units} ${item.unit?.item_value || '单位'} = ¥${item.total_amount || 0}`);
                });
              }
            }
          });
        } else {
          content.push('- 暂无补贴方案数据');
        }
        content.push('');
      }

      // 添加入排标准信息
      if (options.criteria) {
        content.push('## 入排标准');

        // 获取入排标准数据
        try {
          // 导入 ruleDesignerService
          const { ruleDesignerService } = await import('$lib/services/ruleDesignerService');

          // 获取入组标准
          const inclusionQuery = { project_id: projectId, criterion_type: 'inclusion' };
          const inclusionCriteria = await ruleDesignerService.getProjectCriteria(inclusionQuery);

          // 获取排除标准
          const exclusionQuery = { project_id: projectId, criterion_type: 'exclusion' };
          const exclusionCriteria = await ruleDesignerService.getProjectCriteria(exclusionQuery);

          // 处理入组标准
          if (inclusionCriteria && inclusionCriteria.length > 0) {
            content.push('', '### 入组标准');

            // 按分组整理入组标准
            const groupedInclusion = groupCriteriaByGroup(inclusionCriteria);

            groupedInclusion.forEach((group, groupIndex) => {
              if (group.isGroup) {
                // 这是一个OR组
                content.push(`#### 满足以下任一条件（OR组 ${groupIndex + 1}）：`);

                group.criteria.forEach((criterion, criterionIndex) => {
                  const ruleName = criterion.rule_definition.rule_name;
                  const paramValues = formatParameterValues(criterion.criterion.parameter_values);
                  content.push(`${criterionIndex + 1}. **${ruleName}**: ${paramValues}`);
                });
              } else {
                // 单个标准
                const criterion = group.criteria[0];
                const ruleName = criterion.rule_definition.rule_name;
                const paramValues = formatParameterValues(criterion.criterion.parameter_values);
                content.push(`- **${ruleName}**: ${paramValues}`);
              }
            });
          } else {
            content.push('- 暂无入组标准数据');
          }

          // 处理排除标准
          if (exclusionCriteria && exclusionCriteria.length > 0) {
            content.push('', '### 排除标准');

            // 按分组整理排除标准
            const groupedExclusion = groupCriteriaByGroup(exclusionCriteria);

            groupedExclusion.forEach((group, groupIndex) => {
              if (group.isGroup) {
                // 这是一个OR组
                content.push(`#### 满足以下任一条件（OR组 ${groupIndex + 1}）：`);

                group.criteria.forEach((criterion, criterionIndex) => {
                  const ruleName = criterion.rule_definition.rule_name;
                  const paramValues = formatParameterValues(criterion.criterion.parameter_values);
                  content.push(`${criterionIndex + 1}. **${ruleName}**: ${paramValues}`);
                });
              } else {
                // 单个标准
                const criterion = group.criteria[0];
                const ruleName = criterion.rule_definition.rule_name;
                const paramValues = formatParameterValues(criterion.criterion.parameter_values);
                content.push(`- **${ruleName}**: ${paramValues}`);
              }
            });
          } else {
            content.push('- 暂无排除标准数据');
          }
        } catch (criteriaError) {
          console.warn('获取入排标准失败:', criteriaError);
          content.push('- 获取入排标准数据失败');
        }

        content.push('');
      }

      // 文件内容
      const fileContent = content.join('\n');

      // 文件名
      const fileName = `项目详情_${projectDetails.project.project_short_name || projectId}_${new Date().toISOString().slice(0, 10)}.md`;

      // 检查项目路径是否存在
      const projectPath = projectDetails.project.project_path;

      if (projectPath) {
        try {
          // 尝试获取项目路径信息，检查路径是否存在
          await fileSystemService.getFileInfo(projectPath);

          // 构建完整的文件路径
          const filePath = `${projectPath}/${fileName}`;

          // 保存文件到项目文件夹
          const saveResult = await fileSystemService.createFile(filePath, fileContent);

          if (saveResult.success) {
            successMessage = `项目详情已成功导出到: ${filePath}`;

            // 显示打开文件夹选项
            const openFolderConfirm = confirm(`文件已保存到项目文件夹。\n是否打开文件夹？`);
            if (openFolderConfirm) {
              await fileSystemService.openFolder(projectPath);
            }
          } else {
            throw new Error(saveResult.error || '保存文件失败');
          }
        } catch (err) {
          console.error('项目路径不存在或无法访问，将使用浏览器下载:', err);

          // 回退到浏览器下载
          fallbackToBrowserDownload(fileContent, fileName);
        }
      } else {
        console.warn('项目路径未设置，将使用浏览器下载');

        // 回退到浏览器下载
        fallbackToBrowserDownload(fileContent, fileName);
      }
    } catch (err) {
      error = err instanceof Error ? err.message : String(err);
      console.error('导出项目详情失败:', err);
    } finally {
      isLoading = false;
    }
  }

  // 浏览器下载文件的回退方法
  function fallbackToBrowserDownload(content: string, fileName: string) {
    // 创建Blob对象
    const blob = new Blob([content], { type: 'text/markdown;charset=utf-8;' });

    // 创建下载链接
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);

    // 设置下载属性
    link.setAttribute('href', url);
    link.setAttribute('download', fileName);
    link.style.visibility = 'hidden';

    // 添加到文档并触发点击
    document.body.appendChild(link);
    link.click();

    // 清理
    document.body.removeChild(link);
    URL.revokeObjectURL(url);

    successMessage = "项目详情已导出到您的下载文件夹";
  }

  // 导出项目完整数据为JSON
  async function exportProjectDetailsToJSON(options = {
    basicInfo: true,
    sponsors: true,
    drugs: true,
    personnel: true,
    subsidies: true,
    criteria: true
  }) {
    if (!projectDetails) {
      alert('没有可导出的数据');
      return;
    }

    isLoading = true;
    error = null;

    try {
      // 获取完整项目数据（包含入排标准）
      const exportData = await projectManagementService.exportProjectData(projectId);

      if (!exportData) {
        throw new Error('导出数据为空');
      }

      // 根据选项过滤数据
      const filteredData: any = {
        project: { ...exportData.project },
        export_date: exportData.export_date,
        export_version: exportData.export_version
      };

      // 添加基本信息相关字段
      if (options.basicInfo) {
        filteredData.disease = exportData.disease;
        filteredData.project_stage = exportData.project_stage;
        filteredData.project_status = exportData.project_status;
        filteredData.recruitment_status = exportData.recruitment_status;
      }

      // 添加申办方信息
      if (options.sponsors) {
        filteredData.sponsors = exportData.sponsors;
      }

      // 添加研究药物与分组信息
      if (options.drugs) {
        filteredData.research_drugs = exportData.research_drugs;
        filteredData.drug_groups = exportData.drug_groups;
      }

      // 添加研究人员信息
      if (options.personnel) {
        filteredData.personnel = exportData.personnel;
      }

      // 添加补贴信息
      if (options.subsidies) {
        filteredData.subsidy_schemes = exportData.subsidy_schemes;
        filteredData.subsidies = exportData.subsidies;
      }

      // 添加入排标准信息
      if (options.criteria && exportData.criteria) {
        filteredData.criteria = exportData.criteria;
      }

      // 文件内容
      const fileContent = JSON.stringify(filteredData, null, 2);

      // 文件名
      const fileName = `项目数据_${projectDetails.project.project_short_name || projectId}_${new Date().toISOString().slice(0, 10)}.json`;

      // 检查项目路径是否存在
      const projectPath = projectDetails.project.project_path;

      if (projectPath) {
        try {
          // 尝试获取项目路径信息，检查路径是否存在
          await fileSystemService.getFileInfo(projectPath);

          // 构建完整的文件路径
          const filePath = `${projectPath}/${fileName}`;

          // 保存文件到项目文件夹
          const saveResult = await fileSystemService.createFile(filePath, fileContent);

          if (saveResult.success) {
            successMessage = `项目数据已成功导出到: ${filePath}`;

            // 显示打开文件夹选项
            const openFolderConfirm = confirm(`文件已保存到项目文件夹。\n是否打开文件夹？`);
            if (openFolderConfirm) {
              await fileSystemService.openFolder(projectPath);
            }
          } else {
            throw new Error(saveResult.error || '保存文件失败');
          }
        } catch (err) {
          console.error('项目路径不存在或无法访问，将使用浏览器下载:', err);

          // 回退到浏览器下载
          fallbackToBrowserDownloadJSON(fileContent, fileName);
        }
      } else {
        console.warn('项目路径未设置，将使用浏览器下载');

        // 回退到浏览器下载
        fallbackToBrowserDownloadJSON(fileContent, fileName);
      }
    } catch (err) {
      error = err instanceof Error ? err.message : String(err);
      console.error('导出项目数据失败:', err);
    } finally {
      isLoading = false;
    }
  }

  // 浏览器下载JSON文件的回退方法
  function fallbackToBrowserDownloadJSON(content: string, fileName: string) {
    // 创建Blob对象
    const blob = new Blob([content], { type: 'application/json;charset=utf-8;' });

    // 创建下载链接
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);

    // 设置下载属性
    link.setAttribute('href', url);
    link.setAttribute('download', fileName);
    link.style.visibility = 'hidden';

    // 添加到文档并触发点击
    document.body.appendChild(link);
    link.click();

    // 清理
    document.body.removeChild(link);
    URL.revokeObjectURL(url);

    successMessage = "项目数据已导出到您的下载文件夹";
    setTimeout(() => {
      successMessage = null;
    }, 3000);
  }

  // 导出项目详情（显示对话框）
  let showExportDialog = false;
  let exportOptions = {
    basicInfo: true,
    sponsors: true,
    drugs: true,
    personnel: true,
    subsidies: true,
    criteria: true
  };
  let exportFormat = 'markdown'; // 'markdown' or 'json'

  function exportProjectDetails() {
    if (!projectDetails) {
      alert('没有可导出的数据');
      return;
    }

    // 重置导出选项
    exportOptions = {
      basicInfo: true,
      sponsors: true,
      drugs: true,
      personnel: true,
      subsidies: true,
      criteria: true
    };
    exportFormat = 'markdown';

    // 显示导出对话框
    showExportDialog = true;
  }

  // 执行导出
  function executeExport() {
    if (exportFormat === 'markdown') {
      exportProjectDetailsToMarkdown(exportOptions);
    } else {
      exportProjectDetailsToJSON(exportOptions);
    }
    showExportDialog = false;
  }

  // 保存项目
  async function saveProject() {
    if (!projectDetails) return;

    // 验证必填字段
    if (!projectDetails.project.project_name) {
      error = "项目名称为必填项";
      activeTab = 'basic-info';
      return;
    }

    if (!projectDetails.project.project_short_name) {
      error = "项目简称为必填项";
      activeTab = 'basic-info';
      return;
    }

    if (!projectDetails.project.disease_item_id) {
      error = "疾病类型为必填项";
      activeTab = 'basic-info';
      return;
    }

    isSaving = true;
    isLoading = true;
    error = null;
    successMessage = null;

    try {
      // 提取各部分数据
      const {
        project,
        sponsors = [],
        research_drugs = [],
        drug_groups = [],
        personnel = [],
        subsidy_schemes = [],
        subsidies = []
      } = projectDetails;

      // 转换数据格式
      const sponsorsList = sponsors.map((s: any) => ({
        id: s.id,
        project_id: s.project_id,
        sponsor_item_id: s.sponsor_item_id
      }));

      const personnelList = personnel.map((p: any) => ({
        assignment_id: p.assignment_id,
        project_id: p.project_id,
        personnel_id: p.personnel_id,
        role_item_id: p.role_item_id
      }));

      // 保存项目
      const savedProjectId = await projectManagementService.saveProjectWithDetails(
        project,
        sponsorsList,
        research_drugs,
        drug_groups,
        personnelList,
        subsidy_schemes,
        subsidies
      );

      // 清除 localStorage 中的临时数据
      if (projectId === 'new') {
        // 清除研究药物和药物分组的临时数据
        localStorage.removeItem('new_project_research_drugs');
        localStorage.removeItem('new_project_drug_groups');
        console.log('项目保存成功，已清除临时数据');

        // 显示成功消息
        successMessage = "项目创建成功！";

        // 跳转到编辑页面
        goto(`/projects/${savedProjectId}`);
      } else {
        // 显示成功消息
        successMessage = "项目更新成功！";

        // 重新加载项目详情
        await loadProjectDetails();
      }
    } catch (err) {
      error = err instanceof Error ? err.message : String(err);
    } finally {
      isLoading = false;
      isSaving = false;

      // 5秒后自动清除成功消息
      if (successMessage) {
        setTimeout(() => {
          successMessage = null;
        }, 5000);
      }
    }
  }

  // 处理编辑项目
  function handleEditProject() {
    if (!projectDetails?.project.project_id) return;
    goto(`/projects/${projectDetails.project.project_id}/edit`);
  }

  // 处理删除项目
  async function handleDeleteProject() {
    if (!projectDetails?.project.project_id) return;

    showDeleteConfirm = true;
  }

  // 确认删除项目
  async function confirmDeleteProject() {
    if (!projectDetails?.project.project_id) return;

    // 验证输入的项目简称是否匹配
    const projectShortName = projectDetails.project.project_short_name;
    if (confirmProjectName !== projectShortName) {
      deleteConfirmError = "输入的项目简称不匹配，请重新输入";
      return;
    }

    isDeleting = true;
    error = null;
    deleteConfirmError = null;

    try {
      await projectManagementService.deleteProject(projectDetails.project.project_id);
      successMessage = "项目已成功删除！";

      // 延迟跳转，让用户看到成功消息
      setTimeout(() => {
        // 使用已保存的筛选状态返回项目列表
        goto('/projects');
      }, 1500);
    } catch (err) {
      error = err instanceof Error ? err.message : String(err);
      showDeleteConfirm = false;
    } finally {
      isDeleting = false;
    }
  }

  // 取消删除项目
  function cancelDeleteProject() {
    showDeleteConfirm = false;
    confirmProjectName = '';
    deleteConfirmError = null;
  }



  // 计算并格式化药物分组比例
  function calculateRatio(groups: DrugGroup[], currentGroup: DrugGroup): string {
    if (!groups || groups.length <= 1) {
      return currentGroup.share.toString();
    }

    // 计算最大公约数
    function gcd(a: number, b: number): number {
      return b === 0 ? a : gcd(b, a % b);
    }

    // 计算多个数的最大公约数
    function findGCD(numbers: number[]): number {
      let result = numbers[0];
      for (let i = 1; i < numbers.length; i++) {
        result = gcd(result, numbers[i]);
      }
      return result;
    }

    // 获取所有份额
    const shares = groups.map(g => g.share);

    // 计算最大公约数
    const divisor = findGCD(shares);

    // 如果最大公约数为0（可能有份额为0的情况），直接返回原始份额
    if (divisor === 0) {
      return currentGroup.share.toString();
    }

    // 计算简化后的比例
    const simplifiedShares = shares.map(share => share / divisor);

    // 构建比例字符串 (例如 "1:1" 或 "2:1:1")
    return simplifiedShares.join(':');
  }

  // 按角色分组人员
  function groupPersonnelByRole(personnel: any[]) {
    if (!personnel || personnel.length === 0) return [];

    const roleGroups: {
      roleId: number;
      roleName: string;
      personnel: Array<{
        index: number;
        person: any;
      }>;
    }[] = [];

    // 遍历所有人员，按角色分组
    personnel.forEach((person, index) => {
      const roleId = person.role_item_id;
      const roleName = person.role?.item_value || '未知角色';

      // 查找该角色是否已存在于分组中
      let roleGroup = roleGroups.find(group => group.roleId === roleId);

      if (!roleGroup) {
        // 如果角色不存在，创建新的角色分组
        roleGroup = {
          roleId,
          roleName,
          personnel: []
        };
        roleGroups.push(roleGroup);
      }

      // 将人员添加到对应角色分组
      roleGroup.personnel.push({
        index,
        person
      });
    });

    // 按角色名称排序
    roleGroups.sort((a, b) => a.roleName.localeCompare(b.roleName));

    return roleGroups;
  }

  // 按分组整理入排标准
  function groupCriteriaByGroup(criteria: ProjectCriterionWithRule[]) {
    if (!criteria || criteria.length === 0) return [];

    // 结果数组，包含分组和未分组的标准
    const result: {
      isGroup: boolean;
      criteria: ProjectCriterionWithRule[];
    }[] = [];

    // 用于跟踪已处理的标准
    const processedIds = new Set<number>();

    // 首先找出所有具有相同 criteria_group_id 的标准（OR组）
    const groupMap = new Map<string, ProjectCriterionWithRule[]>();

    criteria.forEach(criterion => {
      const groupId = criterion.criterion.criteria_group_id;
      if (groupId) {
        if (!groupMap.has(groupId)) {
          groupMap.set(groupId, []);
        }
        groupMap.get(groupId)?.push(criterion);
      }
    });

    // 添加分组标准
    groupMap.forEach((groupCriteria, _groupId) => {
      if (groupCriteria.length > 1) {
        result.push({
          isGroup: true,
          criteria: groupCriteria
        });

        // 标记这些标准为已处理
        groupCriteria.forEach(c => {
          if (c.criterion.project_criterion_id) {
            processedIds.add(c.criterion.project_criterion_id);
          }
        });
      }
    });

    // 检查标准是否已在分组中
    const isInGroup = (criterion: ProjectCriterionWithRule) => {
      return criterion.criterion.project_criterion_id ?
        processedIds.has(criterion.criterion.project_criterion_id) : false;
    };

    // 添加未分组的标准
    const ungroupedCriteria = criteria.filter(c => !isInGroup(c));
    ungroupedCriteria.forEach(criterion => {
      result.push({
        isGroup: false,
        criteria: [criterion]
      });
    });

    return result;
  }

  // 格式化参数值为可读文本
  function formatParameterValues(paramValuesJson: string): string {
    try {
      const params = JSON.parse(paramValuesJson);

      // 将参数对象转换为可读文本
      const formattedParams = Object.entries(params)
        .map(([key, value]) => {
          // 格式化键名，将驼峰命名转换为空格分隔的单词
          const formattedKey = key
            .replace(/([A-Z])/g, ' $1') // 在大写字母前添加空格
            .replace(/_/g, ' ')         // 将下划线替换为空格
            .trim();                    // 去除首尾空格

          // 首字母大写
          const displayKey = formattedKey.charAt(0).toUpperCase() + formattedKey.slice(1);

          return `${displayKey}: ${value}`;
        })
        .join(', ');

      return formattedParams;
    } catch (error) {
      console.error('解析参数值失败:', error);
      return '参数解析失败';
    }
  }

  // 组件挂载时加载项目详情
  onMount(async () => {
    // 初始化规则设计器表
    try {
      await ruleDesignerService.initTables();
    } catch (err) {
      console.error('初始化规则设计器表失败:', err);
      // 不阻止项目详情加载
    }

    await loadProjectDetails();
  });
</script>

<div class="container mx-auto px-4 py-8">
  <div class="flex justify-between items-center mb-6">
    <div class="flex items-center gap-2">
      <Button variant="ghost" onclick={() => {
        // 使用已保存的筛选状态返回项目列表
        goto('/projects');
      }}>
        <ArrowLeft class="h-4 w-4 mr-2" />
        返回列表
      </Button>
      <h1 class="text-2xl font-bold">
        {projectId === 'new' ? '新建项目' : `项目详情: ${projectDetails?.project.project_short_name || ''}`}
      </h1>
    </div>
    <div class="flex gap-2">
      {#if projectId !== 'new'}
        <Button variant="outline" onclick={() => goto(`/projects/${projectId}/criteria`)}>
          <ClipboardList class="h-4 w-4 mr-2" />
          入排标准
        </Button>

        <Button variant="outline" onclick={exportProjectDetails}>
          <FileDown class="h-4 w-4 mr-2" />
          导出项目
        </Button>
        <Button variant="outline" onclick={handleEditProject}>
          <Pencil class="h-4 w-4 mr-2" />
          编辑项目
        </Button>
        <Button variant="outline" class="text-red-600 hover:bg-red-50" onclick={handleDeleteProject}>
          <Trash class="h-4 w-4 mr-2" />
          删除项目
        </Button>
      {/if}
      <Button onclick={saveProject} disabled={isLoading || isSaving}>
        {#if isSaving}
          <div class="animate-spin h-4 w-4 mr-2 border-2 border-white rounded-full border-t-transparent"></div>
          保存中...
        {:else}
          <Save class="h-4 w-4 mr-2" />
          保存项目
        {/if}
      </Button>
    </div>
  </div>

  <!-- 错误提示 -->
  {#if error}
    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
      <p>{error}</p>
    </div>
  {/if}

  <!-- 成功提示 -->
  {#if successMessage}
    <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
      <p>{successMessage}</p>
    </div>
  {/if}

  <!-- 加载中 -->
  {#if isLoading && !projectDetails}
    <div class="flex justify-center items-center py-12">
      <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900"></div>
    </div>
  {:else if projectDetails}
    <!-- 项目概览仪表板 -->
    {#if projectId !== 'new'}
      <div class="bg-white p-6 rounded-xl shadow-md mb-6 border border-gray-100">
        <div class="mb-5">
          <h2 class="text-2xl font-bold text-gray-800 flex items-center">
            <span class="bg-blue-50 text-blue-600 p-1 rounded-lg mr-2">
              <Clipboard class="h-5 w-5" />
            </span>
            项目概览
          </h2>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-5 gap-5 mb-4">
          <!-- 左侧栏 (占2列) -->
          <div class="md:col-span-2 space-y-5">
            <!-- 上部小卡片区域 - 2x2网格 -->
            <div class="grid grid-cols-1 sm:grid-cols-2 gap-5">
              <!-- 基本信息卡片 -->
              <div class="bg-white border border-gray-100 rounded-xl p-4 shadow-sm hover:shadow-md transition-shadow duration-200">
                <div class="flex items-center mb-3">
                  <span class="bg-blue-50 text-blue-600 p-1.5 rounded-lg mr-2">
                    <Clipboard class="h-5 w-5" />
                  </span>
                  <h3 class="font-bold text-gray-800">基本信息</h3>
                </div>
                <div class="space-y-2 text-sm">
                  <div class="flex justify-between">
                    <span class="text-gray-500 font-medium">项目名称</span>
                    <span class="text-gray-900 font-semibold">{projectDetails.project.project_name || '-'}</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-gray-500 font-medium">项目简称</span>
                    <span class="text-gray-900 font-semibold">{projectDetails.project.project_short_name || '-'}</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-gray-500 font-medium">疾病</span>
                    <span class="text-gray-900">{projectDetails.disease?.item_value || '-'}</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-gray-500 font-medium">研究分期</span>
                    <span class="text-gray-900">{projectDetails.project_stage?.item_value || '-'}</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-gray-500 font-medium">项目状态</span>
                    <span class="text-gray-900">{projectDetails.project_status?.item_value || '-'}</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-gray-500 font-medium">招募状态</span>
                    <span class="text-gray-900">{projectDetails.recruitment_status?.item_value || '-'}</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-gray-500 font-medium">启动日期</span>
                    <span class="text-gray-900">{projectDetails.project.project_start_date || '-'}</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-gray-500 font-medium">最后更新</span>
                    <span class="text-gray-900">{projectDetails.project.last_updated || '-'}</span>
                  </div>
                </div>
              </div>

              <!-- 申办方与合同信息卡片 -->
              <div class="bg-white border border-gray-100 rounded-xl p-4 shadow-sm hover:shadow-md transition-shadow duration-200">
                <div class="flex items-center mb-3">
                  <span class="bg-emerald-50 text-emerald-600 p-1.5 rounded-lg mr-2">
                    <Building class="h-5 w-5" />
                  </span>
                  <h3 class="font-bold text-gray-800">申办方与合同信息</h3>
                </div>
                <div class="space-y-3">
                  <!-- 申办方部分 -->
                  <div>
                    <div class="text-sm text-gray-500 font-medium mb-2">申办方</div>
                    {#if projectDetails.sponsors && projectDetails.sponsors.length > 0}
                      <div class="flex flex-wrap gap-1.5">
                        {#each projectDetails.sponsors as sponsor}
                          <span class="bg-emerald-50 text-emerald-700 text-xs px-2.5 py-1 rounded-full font-medium border border-emerald-100">
                            {sponsor.sponsor?.item_value || '未知申办方'}
                          </span>
                        {/each}
                      </div>
                    {:else}
                      <div class="text-gray-400 text-sm italic">暂无申办方数据</div>
                    {/if}
                  </div>

                  <!-- 合同信息部分 -->
                  <div class="pt-2 border-t border-gray-100">
                    <div class="text-sm text-gray-500 font-medium mb-2">合同信息</div>
                    <div class="flex gap-6 text-sm">
                      <div class="flex flex-col">
                        <span class="text-gray-500 text-xs">中心数</span>
                        <span class="font-bold text-emerald-600 text-lg">{projectDetails.project.contract_case_center || 0}</span>
                      </div>
                      <div class="flex flex-col">
                        <span class="text-gray-500 text-xs">例数</span>
                        <span class="font-bold text-emerald-600 text-lg">{projectDetails.project.contract_case_total || 0}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 研究药物与分组 -->
              <div class="bg-white border border-gray-100 rounded-xl p-4 shadow-sm hover:shadow-md transition-shadow duration-200">
                <div class="flex items-center mb-3">
                  <span class="bg-indigo-50 text-indigo-600 p-1.5 rounded-lg mr-2">
                    <Pill class="h-5 w-5" />
                  </span>
                  <h3 class="font-bold text-gray-800">研究药物信息</h3>
                </div>
                <div class="space-y-3 text-sm">
                  <!-- 研究药物部分 - 增强显示 -->
                  {#if projectDetails.research_drugs && projectDetails.research_drugs.length > 0}
                    <div>
                      <div class="text-sm text-gray-500 font-medium mb-2">研究药物</div>
                      <div class="flex flex-wrap gap-1.5">
                        {#each projectDetails.research_drugs as drug}
                          <Tooltip.Root>
                            <Tooltip.Trigger>
                              <div class="group relative bg-indigo-50 text-indigo-700 text-xs px-2.5 py-1 rounded-full font-medium border border-indigo-100 hover:bg-indigo-100 transition-colors cursor-help">
                                {drug.research_drug}
                                {#if drug.drug_classification || drug.usage_method || drug.dosage}
                                  <div class="inline-block w-1 h-1 bg-indigo-400 rounded-full ml-1"></div>
                                {/if}
                              </div>
                            </Tooltip.Trigger>
                            <Tooltip.Content side="top" class="max-w-sm">
                              <div class="p-3 space-y-2 text-sm">
                                <div class="font-semibold text-gray-900 border-b border-gray-200 pb-1">
                                  {drug.research_drug}
                                </div>
                                
                                <!-- 药物类型（新增） -->
                                {#if drug.drug_type?.item_value}
                                  <div class="flex justify-between">
                                    <span class="text-gray-500">类型:</span>
                                    <span class="text-indigo-700 font-semibold">{drug.drug_type.item_value}</span>
                                  </div>
                                {/if}
                                
                                {#if drug.drug_classification?.item_value}
                                  <div class="flex justify-between">
                                    <span class="text-gray-500">分类:</span>
                                    <span class="text-gray-900 font-medium">{drug.drug_classification.item_value}</span>
                                  </div>
                                {/if}
                                {#if drug.usage_method?.item_value}
                                  <div class="flex justify-between">
                                    <span class="text-gray-500">用法:</span>
                                    <span class="text-gray-900 font-medium">{drug.usage_method.item_value}</span>
                                  </div>
                                {/if}
                                {#if drug.usage_frequency?.item_value}
                                  <div class="flex justify-between">
                                    <span class="text-gray-500">频率:</span>
                                    <span class="text-gray-900 font-medium">{drug.usage_frequency.item_value}</span>
                                  </div>
                                {/if}
                                {#if drug.dosage}
                                  <div class="flex justify-between">
                                    <span class="text-gray-500">剂量:</span>
                                    <span class="text-gray-900 font-medium">{drug.dosage}</span>
                                  </div>
                                {/if}
                                {#if drug.administration_route?.item_value}
                                  <div class="flex justify-between">
                                    <span class="text-gray-500">途径:</span>
                                    <span class="text-gray-900 font-medium">{drug.administration_route.item_value}</span>
                                  </div>
                                {/if}
                                {#if drug.duration}
                                  <div class="flex justify-between">
                                    <span class="text-gray-500">持续:</span>
                                    <span class="text-gray-900 font-medium">{drug.duration}</span>
                                  </div>
                                {/if}
                                
                                <!-- 份额信息（新增） -->
                                {#if drug.share !== undefined && drug.share !== null}
                                  <div class="flex justify-between">
                                    <span class="text-gray-500">份额:</span>
                                    <span class="text-purple-700 font-semibold">{drug.share}</span>
                                  </div>
                                {/if}
                                
                                <!-- 药物特性（新增） -->
                                {#if drug.drug_characteristics}
                                  <div class="pt-1 border-t border-gray-200">
                                    <span class="text-gray-500 text-xs">特性:</span>
                                    <div class="text-gray-900 text-xs mt-1 leading-relaxed">{drug.drug_characteristics}</div>
                                  </div>
                                {/if}
                                
                                {#if drug.notes}
                                  <div class="pt-1 border-t border-gray-200">
                                    <span class="text-gray-500 text-xs">备注:</span>
                                    <div class="text-gray-900 text-xs mt-1 leading-relaxed">{drug.notes}</div>
                                  </div>
                                {/if}
                                
                                {#if !drug.drug_type && !drug.drug_classification && !drug.usage_method && !drug.dosage && !drug.administration_route && !drug.duration && !drug.share && !drug.drug_characteristics && !drug.notes}
                                  <div class="text-gray-400 italic text-xs">暂无详细信息</div>
                                {/if}
                              </div>
                            </Tooltip.Content>
                          </Tooltip.Root>
                        {/each}
                      </div>
                    </div>
                  {:else}
                    <div class="text-gray-400 text-sm italic">暂无研究药物</div>
                  {/if}

                  <!-- 药物份额统计 - 显示研究药物中的份额信息 -->
                  {#if projectDetails.research_drugs && projectDetails.research_drugs.some(drug => drug.share !== undefined && drug.share !== null)}
                    {@const drugsWithShare = projectDetails.research_drugs.filter(drug => drug.share !== undefined && drug.share !== null)}
                    <div class="pt-2 border-t border-gray-100">
                      <div class="text-sm text-gray-500 font-medium mb-2">药物份额分配</div>

                      {#if drugsWithShare.length > 1}
                        {@const totalShare = drugsWithShare.reduce((sum, drug) => sum + (drug.share || 0), 0)}
                        <div class="mb-2">
                          <span class="bg-purple-100 text-purple-800 text-xs px-2.5 py-1 rounded-full font-bold border border-purple-200">
                            总份额: {totalShare}
                          </span>
                        </div>
                      {/if}

                      <div class="space-y-2">
                        {#each drugsWithShare as drug}
                          {@const totalShareForCalc = drugsWithShare.reduce((sum, d) => sum + (d.share || 0), 0)}
                          <div>
                            <div class="flex justify-between text-sm mb-1">
                              <span class="font-medium">
                                {drug.research_drug}
                                {#if drug.drug_type?.item_value}
                                  <span class="ml-1 text-xs text-indigo-600">({drug.drug_type.item_value})</span>
                                {/if}
                              </span>
                              <span class="font-bold text-purple-700">
                                {drug.share}
                                {#if drugsWithShare.length > 1}
                                  <span class="text-gray-500 font-normal ml-1">
                                    ({totalShareForCalc > 0 ? Math.round(((drug.share || 0) / totalShareForCalc) * 100) : 0}%)
                                  </span>
                                {/if}
                              </span>
                            </div>
                            <div class="w-full bg-gray-100 rounded-full h-1.5">
                              <div class="bg-purple-500 h-1.5 rounded-full" style="width: {totalShareForCalc > 0 ? Math.min(100, ((drug.share || 0) / totalShareForCalc) * 100) : 0}%"></div>
                            </div>
                          </div>
                        {/each}
                      </div>
                    </div>
                  {:else if !projectDetails.research_drugs || projectDetails.research_drugs.length === 0}
                    <div class="text-gray-400 text-sm italic">暂无药物数据</div>
                  {/if}
                </div>
              </div>

              <!-- 补贴信息 -->
              <div class="bg-white border border-gray-100 rounded-xl p-4 shadow-sm hover:shadow-md transition-shadow duration-200">
                <div class="flex items-center mb-3">
                  <span class="bg-amber-50 text-amber-600 p-1.5 rounded-lg mr-2">
                    <DollarSign class="h-5 w-5" />
                  </span>
                  <h3 class="font-bold text-gray-800">补贴信息</h3>
                </div>
                <div>
                  {#if projectDetails?.subsidy_schemes && projectDetails.subsidy_schemes.length > 0}
                    {@const schemes = projectDetails.subsidy_schemes}
                    {@const subsidies = projectDetails.subsidies}
                    <div class="space-y-2">
                      <div class="text-sm text-gray-500 font-medium mb-2">补贴方案</div>
                      <!-- 方案列表 -->
                      <div class="space-y-1.5">
                        {#each schemes.slice(0, 3) as scheme (scheme.scheme_id || scheme.scheme_name)}
                          <Tooltip.Root>
                            <Tooltip.Trigger class="w-full">
                              <div class="flex justify-between py-1.5 border-b border-gray-100 last:border-0 cursor-default hover:bg-amber-50/30 px-2 rounded transition-colors duration-150">
                                <span class="truncate max-w-[70%] font-medium" title={scheme.scheme_name || '未命名方案'}>
                                  {scheme.scheme_name || '未命名方案'}
                                </span>
                                <span class="font-bold text-amber-600">¥{scheme.total_amount || 0}</span>
                              </div>
                            </Tooltip.Trigger>
                            <Tooltip.Content class="bg-white border shadow-lg rounded-xl p-4 z-50">
                              <p class="font-bold mb-3 text-base text-gray-800">{scheme.scheme_name || '未命名方案'} 详情</p>
                              {#if scheme.included_subsidies && scheme.included_subsidies.length > 0 && subsidies}
                                {@const includedItems = subsidies.filter(sub => scheme.included_subsidies?.includes(sub.subsidy_item_id || -1)) || []}
                                {#if includedItems.length > 0}
                                  <ul class="space-y-2 list-none pl-0">
                                    {#each includedItems as item}
                                      <li class="bg-amber-50/50 p-2 rounded-lg border border-amber-100">
                                        <div class="font-semibold text-gray-800">{item.subsidy_type?.item_value || '未知类型'}</div>
                                        <div class="text-sm text-gray-600">
                                          {item.unit_amount} × {item.total_units} {item.unit?.item_value || '单位'} =
                                          <span class="font-bold text-amber-600">¥{item.total_amount || 0}</span>
                                        </div>
                                      </li>
                                    {/each}
                                  </ul>
                                {:else}
                                  <p class="text-gray-500 text-sm italic">此方案当前未关联有效的补贴项。</p>
                                {/if}
                              {:else}
                                <p class="text-gray-500 text-sm italic">此方案未包含任何补贴项。</p>
                              {/if}
                            </Tooltip.Content>
                          </Tooltip.Root>
                        {/each}
                        {#if schemes.length > 3}
                          <div class="text-amber-600 text-xs text-center mt-2 font-medium">
                            还有 {schemes.length - 3} 个方案...
                          </div>
                        {/if}
                      </div>
                    </div>
                  {:else}
                    <div class="text-gray-400 text-sm italic">暂无补贴方案数据</div>
                  {/if}
                </div>
              </div>
            </div>

            <!-- 研究人员卡片 - 占据左侧栏下部 -->
            <div class="bg-white border border-gray-100 rounded-xl p-4 shadow-sm hover:shadow-md transition-shadow duration-200">
              <div class="flex items-center mb-3">
                <span class="bg-rose-50 text-rose-600 p-1.5 rounded-lg mr-2">
                  <Users class="h-5 w-5" />
                </span>
                <h3 class="font-bold text-gray-800">研究人员</h3>
              </div>
              <div class="space-y-4">
                {#if projectDetails.personnel && projectDetails.personnel.length > 0}
                  <div class="grid grid-cols-1 sm:grid-cols-2 gap-5 text-sm">
                    {#each groupPersonnelByRole(projectDetails.personnel) as roleGroup}
                      <div class="bg-gray-50/70 p-3 rounded-lg border border-gray-100">
                        <div class="flex items-center mb-2">
                          <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-semibold bg-rose-100 text-rose-800 border border-rose-200">
                            {roleGroup.roleName}
                          </span>
                          <span class="ml-2 text-xs text-gray-500">{roleGroup.personnel.length} 人</span>
                        </div>
                        <div class="flex flex-wrap gap-1.5">
                          {#each roleGroup.personnel as { person }}
                            <span class="inline-flex items-center px-2.5 py-1 rounded-md text-xs font-medium bg-white text-gray-700 border border-gray-200 shadow-sm">
                              {person.personnel?.name || '未知人员'}
                            </span>
                          {/each}
                        </div>
                      </div>
                    {/each}
                  </div>
                {:else}
                  <div class="text-gray-400 text-sm italic p-4 text-center bg-gray-50 rounded-lg">暂无研究人员数据</div>
                {/if}
              </div>
            </div>
          </div>

          <!-- 右侧栏 - 入组/排除标准卡片 (占3列) -->
          <div class="md:col-span-3 h-full flex">
            <div class="h-full w-full bg-white border border-gray-100 rounded-xl p-5 shadow-sm hover:shadow-md transition-shadow duration-200 flex flex-col" style="height: 650px;">
              <div class="flex items-center justify-between mb-4">
                <div class="flex items-center">
                  <span class="bg-cyan-50 text-cyan-600 p-1.5 rounded-lg mr-2">
                    <ClipboardList class="h-5 w-5" />
                  </span>
                  <h3 class="font-bold text-gray-800">入组/排除标准</h3>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  class="bg-white shadow-sm hover:bg-cyan-50 text-cyan-700 border-cyan-200"
                  onclick={() => goto(`/projects/${projectId}/criteria`)}
                >
                  <ClipboardList class="h-3.5 w-3.5 mr-1" />
                  查看全部
                </Button>
              </div>

              <!-- 内容区域 - 设置高度填充并允许滚动 -->
              <div class="flex-grow overflow-hidden bg-gray-50/50 rounded-lg p-4" style="height: calc(100% - 50px);">
                <ProjectCriteriaSummary projectId={projectId} />
              </div>
            </div>
          </div>
        </div>
      </div>
    {/if}

    <!-- 项目详情标签页 -->
    <Tabs.Root bind:value={activeTab} class="w-full">
      <Tabs.List class="grid grid-cols-6 mb-6">
        <Tabs.Trigger value="basic-info">基本信息</Tabs.Trigger>
        <Tabs.Trigger value="sponsors">申办方</Tabs.Trigger>
        <Tabs.Trigger value="drugs">研究药物</Tabs.Trigger>
        <Tabs.Trigger value="personnel">研究人员</Tabs.Trigger>
        <Tabs.Trigger value="subsidies">补贴信息</Tabs.Trigger>
        <Tabs.Trigger value="other-info">其他信息</Tabs.Trigger>
      </Tabs.List>

      <div class="bg-white p-6 rounded-lg shadow">
        <Tabs.Content value="basic-info">
          <ProjectBasicInfo projectDetails={projectDetails} />
        </Tabs.Content>

        <Tabs.Content value="sponsors">
          <ProjectSponsors projectDetails={projectDetails} />
        </Tabs.Content>

        <Tabs.Content value="drugs">
          <ProjectDrugs projectDetails={projectDetails} />
        </Tabs.Content>

        <Tabs.Content value="personnel">
          <ProjectPersonnel {projectDetails} />
        </Tabs.Content>

        <Tabs.Content value="subsidies">
          <ProjectSubsidies
            subsidies={projectDetails?.subsidies || []}
            schemes={projectDetails?.subsidy_schemes || []}
            project_id={projectDetails?.project?.project_id || ''}
          />
        </Tabs.Content>

        <Tabs.Content value="other-info">
          <ProjectRecruitmentPolicies
            projectId={projectDetails?.project?.project_id || ''}
          />
        </Tabs.Content>
      </div>
    </Tabs.Root>

    <!-- 项目文件浏览器 (放在页面底部) -->
    {#if projectId !== 'new' && projectDetails?.project?.project_path}
      <div class="mt-6 bg-white rounded-lg shadow">
        <div class="border-b border-gray-200 px-6 py-4 flex justify-between items-center">
          <div class="flex items-center">
            <span class="bg-gray-100 text-gray-600 p-1 rounded-lg mr-2">
              <FileText class="h-4 w-4" />
            </span>
            <h3 class="text-lg font-medium text-gray-800">项目文件</h3>
          </div>
        </div>
        <div class="p-4">
          <ProjectFileExplorer projectPath={projectDetails.project.project_path} />
        </div>
      </div>
    {/if}
  {/if}

  <!-- 删除确认对话框 -->
  {#if showDeleteConfirm}
    <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg p-6 max-w-md w-full">
        <h3 class="text-xl font-bold mb-4">确认删除项目</h3>
        <p class="mb-4">您确定要删除项目 <span class="font-semibold">{projectDetails?.project.project_short_name || ''}</span> 吗？此操作将删除所有相关数据，且不可恢复。</p>

        {#if projectDetails?.sponsors && projectDetails.sponsors.length > 0}
          <div class="mb-4 text-sm">
            <p class="font-semibold mb-1">将删除以下关联数据：</p>
            <ul class="list-disc pl-5 space-y-1">
              <li>{projectDetails.sponsors.length} 个申办方记录</li>
              {#if projectDetails.research_drugs && projectDetails.research_drugs.length > 0}
                <li>{projectDetails.research_drugs.length} 个研究药物记录</li>
              {/if}
              {#if projectDetails.drug_groups && projectDetails.drug_groups.length > 0}
                <li>{projectDetails.drug_groups.length} 个药物分组记录</li>
              {/if}
              {#if projectDetails.personnel && projectDetails.personnel.length > 0}
                <li>{projectDetails.personnel.length} 个人员分配记录</li>
              {/if}
              {#if projectDetails.subsidies && projectDetails.subsidies.length > 0}
                <li>{projectDetails.subsidies.length} 个补贴记录</li>
              {/if}
            </ul>
          </div>
        {/if}

        <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-4">
          <div class="flex">
            <div class="flex-shrink-0">
              <svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
              </svg>
            </div>
            <div class="ml-3">
              <p class="text-sm text-yellow-700">
                为确认删除，请输入项目简称：<strong>{projectDetails?.project.project_short_name || ''}</strong>
              </p>
            </div>
          </div>
        </div>

        <div class="mb-4">
          <label for="confirmProjectName" class="block text-sm font-medium text-gray-700 mb-1">
            项目简称
          </label>
          <div class="flex gap-2">
            <div class="flex-grow">
              <Input
                id="confirmProjectName"
                type="text"
                placeholder="请输入项目简称以确认删除"
                bind:value={confirmProjectName}
                class={deleteConfirmError ? "border-red-500" : ""}
              />
            </div>
            <Button
              variant="outline"
              size="sm"
              class="whitespace-nowrap"
              onclick={() => confirmProjectName = projectDetails?.project.project_short_name || ''}
            >
              自动填入
            </Button>
          </div>
          {#if deleteConfirmError}
            <p class="mt-1 text-sm text-red-600">{deleteConfirmError}</p>
            <p class="text-xs text-gray-500">正确的项目简称是: <span class="font-mono bg-gray-100 px-1 rounded">{projectDetails?.project.project_short_name}</span></p>
          {/if}
        </div>

        <div class="flex justify-end gap-2">
          <Button variant="outline" onclick={cancelDeleteProject} disabled={isDeleting}>
            取消
          </Button>
          <Button variant="destructive" onclick={confirmDeleteProject} disabled={isDeleting || !confirmProjectName}>
            {#if isDeleting}
              <div class="animate-spin h-4 w-4 mr-2 border-2 border-white rounded-full border-t-transparent"></div>
              删除中...
            {:else}
              确认删除
            {/if}
          </Button>
        </div>
      </div>
    </div>
  {/if}

  <!-- 导出项目对话框 -->
  {#if showExportDialog}
    <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg p-6 max-w-md w-full">
        <h3 class="text-xl font-bold mb-4">导出项目数据</h3>
        <p class="mb-4">请选择要导出的内容和格式</p>

        {#if projectDetails?.project.project_path}
          <div class="bg-blue-50 border-l-4 border-blue-400 p-4 mb-4">
            <div class="flex">
              <div class="flex-shrink-0">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 19a2 2 0 01-2-2V7a2 2 0 012-2h4l2 2h4a2 2 0 012 2v1M5 19h14a2 2 0 002-2v-5a2 2 0 00-2-2H9a2 2 0 00-2 2v5a2 2 0 01-2 2z" />
                </svg>
              </div>
              <div class="ml-3">
                <p class="text-sm text-blue-700">
                  文件将保存到项目文件夹：<br />
                  <span class="font-mono text-xs bg-blue-100 px-1 py-0.5 rounded">{projectDetails.project.project_path}</span>
                </p>
              </div>
            </div>
          </div>
        {:else}
          <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-4">
            <div class="flex">
              <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                </svg>
              </div>
              <div class="ml-3">
                <p class="text-sm text-yellow-700">
                  项目路径未设置，文件将保存到您的下载文件夹
                </p>
              </div>
            </div>
          </div>
        {/if}

        <div class="mb-6">
          <h4 class="font-medium mb-2">导出内容</h4>
          <div class="space-y-2">
            <div class="flex items-center">
              <input
                type="checkbox"
                id="export-basic-info"
                class="w-4 h-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500"
                checked={exportOptions.basicInfo}
                onchange={(e) => exportOptions.basicInfo = (e.target as HTMLInputElement).checked}
              />
              <label for="export-basic-info" class="ml-2 text-sm font-medium text-gray-700">基本信息</label>
            </div>
            <div class="flex items-center">
              <input
                type="checkbox"
                id="export-sponsors"
                class="w-4 h-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500"
                checked={exportOptions.sponsors}
                onchange={(e) => exportOptions.sponsors = (e.target as HTMLInputElement).checked}
              />
              <label for="export-sponsors" class="ml-2 text-sm font-medium text-gray-700">申办方与合同信息</label>
            </div>
            <div class="flex items-center">
              <input
                type="checkbox"
                id="export-drugs"
                class="w-4 h-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500"
                checked={exportOptions.drugs}
                onchange={(e) => exportOptions.drugs = (e.target as HTMLInputElement).checked}
              />
              <label for="export-drugs" class="ml-2 text-sm font-medium text-gray-700">研究药物与分组</label>
            </div>
            <div class="flex items-center">
              <input
                type="checkbox"
                id="export-personnel"
                class="w-4 h-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500"
                checked={exportOptions.personnel}
                onchange={(e) => exportOptions.personnel = (e.target as HTMLInputElement).checked}
              />
              <label for="export-personnel" class="ml-2 text-sm font-medium text-gray-700">研究人员</label>
            </div>
            <div class="flex items-center">
              <input
                type="checkbox"
                id="export-subsidies"
                class="w-4 h-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500"
                checked={exportOptions.subsidies}
                onchange={(e) => exportOptions.subsidies = (e.target as HTMLInputElement).checked}
              />
              <label for="export-subsidies" class="ml-2 text-sm font-medium text-gray-700">补贴信息</label>
            </div>
            <div class="flex items-center">
              <input
                type="checkbox"
                id="export-criteria"
                class="w-4 h-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500"
                checked={exportOptions.criteria}
                onchange={(e) => exportOptions.criteria = (e.target as HTMLInputElement).checked}
              />
              <label for="export-criteria" class="ml-2 text-sm font-medium text-gray-700">入排标准</label>
            </div>
          </div>
        </div>

        <div class="mb-6">
          <h4 class="font-medium mb-2">导出格式</h4>
          <div class="space-y-2">
            <div class="flex items-center">
              <input
                type="radio"
                id="format-markdown"
                name="export-format"
                class="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                checked={exportFormat === 'markdown'}
                onchange={() => exportFormat = 'markdown'}
              />
              <label for="format-markdown" class="ml-2 text-sm font-medium text-gray-700 flex items-center">
                <FileText class="h-4 w-4 mr-1 text-blue-500" />
                Markdown 格式 (.md)
              </label>
            </div>
            <div class="flex items-center">
              <input
                type="radio"
                id="format-json"
                name="export-format"
                class="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                checked={exportFormat === 'json'}
                onchange={() => exportFormat = 'json'}
              />
              <label for="format-json" class="ml-2 text-sm font-medium text-gray-700 flex items-center">
                <FileText class="h-4 w-4 mr-1 text-green-500" />
                JSON 格式 (.json)
              </label>
            </div>
          </div>
        </div>

        <div class="flex justify-end gap-2">
          <Button variant="outline" onclick={() => showExportDialog = false}>
            取消
          </Button>
          <Button onclick={executeExport} disabled={isLoading}>
            {#if isLoading}
              <div class="animate-spin h-4 w-4 mr-2 border-2 border-white rounded-full border-t-transparent"></div>
              导出中...
            {:else}
              导出
            {/if}
          </Button>
        </div>
      </div>
    </div>
  {/if}
</div>
